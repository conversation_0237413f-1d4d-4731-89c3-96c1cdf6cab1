# نظام إدارة شؤون الموظفين

نظام ويب متكامل لإدارة شؤون الموظفين باستخدام Python Flask مع دعم اللغة العربية والإنجليزية.

## الميزات الرئيسية

### 🏢 إدارة الموظفين
- تسجيل الموظفين الجدد مع البيانات الشخصية والوظيفية
- تعديل وتحديث بيانات الموظفين
- رفع وإدارة صور الموظفين
- دعم الأسماء باللغتين العربية والإنجليزية
- تتبع حالة الموظف (نشط، غير نشط، منتهي الخدمة)

### 💰 إدارة الرواتب
- إنشاء وإدارة كشوف الرواتب الشهرية
- حساب الراتب الأساسي، البدلات، العمل الإضافي، المكافآت
- إدارة الخصومات، الضرائب، والتأمينات
- طباعة كشوف الرواتب بصيغة PDF
- تصدير البيانات إلى Excel
- إنشاء الرواتب الشهرية بشكل جماعي

### 🏖️ إدارة الإجازات
- تقديم طلبات الإجازات (سنوية، مرضية، طارئة، أمومة)
- اعتماد أو رفض طلبات الإجازات
- تتبع أيام الإجازة المستخدمة والمتبقية
- عرض تقويم الإجازات

### ⏰ نظام الحضور والانصراف
- تسجيل أوقات الحضور والانصراف
- حساب ساعات العمل والعمل الإضافي
- تتبع حالات التأخير والغياب
- تقارير الحضور اليومية والشهرية

### 📊 تقييم الأداء
- إنشاء تقييمات دورية للموظفين
- تقييم متعدد المعايير (الجودة، الإنتاجية، العمل الجماعي، التواصل، القيادة)
- حفظ تاريخ التقييمات السابقة
- تقارير الأداء والإحصائيات

### 📁 إدارة المستندات
- رفع وتنظيم مستندات الموظفين
- دعم أنواع ملفات متعددة (PDF, DOC, صور)
- تصنيف المستندات حسب النوع
- تحميل وعرض المستندات

### 📈 لوحة التحكم والتقارير
- لوحة تحكم شاملة مع الإحصائيات الرئيسية
- تقارير مفصلة للموظفين والرواتب والحضور
- رسوم بيانية وإحصائيات تفاعلية
- تصدير التقارير

### 🌐 دعم متعدد اللغات
- واجهة باللغتين العربية والإنجليزية
- تبديل سهل بين اللغات
- دعم النصوص من اليمين إلى اليسار (RTL)

### 🔐 نظام الصلاحيات
- ثلاثة مستويات صلاحيات: مدير، موارد بشرية، مستخدم
- حماية الصفحات والوظائف حسب الصلاحيات
- نظام تسجيل دخول آمن

## متطلبات النظام

- Python 3.8 أو أحدث
- Flask 2.3+
- SQLite (افتراضي) أو PostgreSQL
- متصفح ويب حديث

## التثبيت والإعداد

### 1. تحميل المشروع
```bash
git clone <repository-url>
cd anwar
```

### 2. إنشاء بيئة افتراضية
```bash
python -m venv venv

# على Windows
venv\Scripts\activate

# على Linux/Mac
source venv/bin/activate
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. إعداد قاعدة البيانات
```bash
# سيتم إنشاء قاعدة البيانات تلقائياً عند أول تشغيل
python app.py
```

### 5. تشغيل التطبيق
```bash
python app.py
```

سيعمل التطبيق على العنوان: `http://localhost:5000`

## بيانات تسجيل الدخول الافتراضية

- **اسم المستخدم:** admin
- **كلمة المرور:** admin123
- **الصلاحية:** مدير النظام

## إعداد قاعدة البيانات

### استخدام SQLite (افتراضي)
لا يتطلب إعداد إضافي، سيتم إنشاء ملف `hr_system.db` تلقائياً.

### استخدام PostgreSQL
1. إنشاء قاعدة بيانات PostgreSQL
2. تعديل متغير البيئة:
```bash
export DATABASE_URL="postgresql://username:password@localhost/hr_system"
```

## هيكل المشروع

```
anwar/
├── app.py                 # التطبيق الرئيسي
├── config.py             # إعدادات التطبيق
├── models.py             # نماذج قاعدة البيانات
├── requirements.txt      # المتطلبات
├── blueprints/          # وحدات التطبيق
│   ├── auth.py          # المصادقة
│   ├── dashboard.py     # لوحة التحكم
│   ├── employees.py     # إدارة الموظفين
│   ├── payroll.py       # إدارة الرواتب
│   ├── leaves.py        # إدارة الإجازات
│   ├── attendance.py    # الحضور والانصراف
│   ├── performance.py   # تقييم الأداء
│   └── documents.py     # إدارة المستندات
├── templates/           # قوالب HTML
│   ├── base.html        # القالب الأساسي
│   ├── auth/           # قوالب المصادقة
│   ├── dashboard/      # قوالب لوحة التحكم
│   ├── employees/      # قوالب الموظفين
│   ├── payroll/        # قوالب الرواتب
│   ├── leaves/         # قوالب الإجازات
│   ├── attendance/     # قوالب الحضور
│   ├── performance/    # قوالب التقييم
│   └── documents/      # قوالب المستندات
└── static/             # الملفات الثابتة
    └── uploads/        # ملفات الرفع
```

## الاستخدام

### إضافة موظف جديد
1. انتقل إلى "الموظفون" > "إضافة موظف جديد"
2. املأ البيانات المطلوبة
3. ارفع صورة الموظف (اختياري)
4. احفظ البيانات

### إنشاء كشف راتب
1. انتقل إلى "الرواتب" > "إضافة راتب"
2. اختر الموظف والشهر/السنة
3. أدخل تفاصيل الراتب
4. احفظ واطبع كشف الراتب

### إدارة الإجازات
1. انتقل إلى "الإجازات" > "إضافة طلب إجازة"
2. اختر الموظف ونوع الإجازة
3. حدد تواريخ البداية والنهاية
4. اعتمد أو ارفض الطلب

## الأمان

- تشفير كلمات المرور
- حماية من CSRF
- تحديد صلاحيات المستخدمين
- تحقق من أنواع الملفات المرفوعة
- تنظيف المدخلات

## الدعم الفني

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى إنشاء issue في المستودع.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

---

© 2024 نظام إدارة شؤون الموظفين. جميع الحقوق محفوظة.
