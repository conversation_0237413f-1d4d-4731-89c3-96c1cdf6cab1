<!DOCTYPE html>
<html lang="{{ CURRENT_LANGUAGE }}" dir="{{ 'rtl' if CURRENT_LANGUAGE == 'ar' else 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة شؤون الموظفين{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Arabic Font -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar-brand {
            font-weight: 600;
        }
        
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #343a40;
        }
        
        .sidebar .nav-link {
            color: #adb5bd;
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin: 0.125rem 0.5rem;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #495057;
        }
        
        .sidebar .nav-link i {
            margin-left: 0.5rem;
            width: 1.25rem;
        }
        
        .main-content {
            margin-right: 0;
        }
        
        @media (min-width: 768px) {
            .main-content {
                margin-right: 250px;
            }
        }
        
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        
        .card-header {
            background-color: #fff;
            border-bottom: 1px solid #dee2e6;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        
        .btn-primary:hover {
            background-color: #0b5ed7;
            border-color: #0a58ca;
        }
        
        .table th {
            border-top: none;
            font-weight: 500;
            background-color: #f8f9fa;
        }
        
        .badge {
            font-size: 0.75em;
        }
        
        .alert {
            border: none;
            border-radius: 0.5rem;
        }
        
        .form-label {
            font-weight: 500;
            margin-bottom: 0.5rem;
        }
        
        .form-control:focus {
            border-color: #86b7fe;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }
        
        .language-switcher {
            margin-right: 1rem;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 1rem;
        }
        
        .stats-card-2 {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 1rem;
        }
        
        .stats-card-3 {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border-radius: 1rem;
        }
        
        .stats-card-4 {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
            border-radius: 1rem;
        }
        
        .profile-img {
            width: 40px;
            height: 40px;
            object-fit: cover;
            border-radius: 50%;
        }
        
        .employee-profile-img {
            width: 150px;
            height: 150px;
            object-fit: cover;
            border-radius: 50%;
        }
        
        .text-truncate-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard.index') }}">
                <i class="bi bi-building"></i>
                نظام إدارة شؤون الموظفين
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <!-- Language Switcher -->
                    <li class="nav-item dropdown language-switcher">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-translate"></i>
                            {{ LANGUAGES[CURRENT_LANGUAGE] }}
                        </a>
                        <ul class="dropdown-menu">
                            {% for code, name in LANGUAGES.items() %}
                            <li>
                                <a class="dropdown-item {% if code == CURRENT_LANGUAGE %}active{% endif %}" 
                                   href="{{ url_for('set_language', language=code) }}">
                                    {{ name }}
                                </a>
                            </li>
                            {% endfor %}
                        </ul>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    {% if current_user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i>
                            {{ current_user.username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="bi bi-person"></i> الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-gear"></i> الإعدادات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}"><i class="bi bi-box-arrow-right"></i> تسجيل الخروج</a></li>
                        </ul>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid" style="margin-top: 56px;">
        <div class="row">
            {% if current_user.is_authenticated %}
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if request.endpoint == 'dashboard.index' }}" href="{{ url_for('dashboard.index') }}">
                                <i class="bi bi-speedometer2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'employees' in request.endpoint }}" href="{{ url_for('employees.index') }}">
                                <i class="bi bi-people"></i>
                                الموظفون
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'payroll' in request.endpoint }}" href="{{ url_for('payroll.index') }}">
                                <i class="bi bi-cash-stack"></i>
                                الرواتب
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'leaves' in request.endpoint }}" href="{{ url_for('leaves.index') }}">
                                <i class="bi bi-calendar-x"></i>
                                الإجازات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'attendance' in request.endpoint }}" href="{{ url_for('attendance.index') }}">
                                <i class="bi bi-clock"></i>
                                الحضور والانصراف
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'performance' in request.endpoint }}" href="{{ url_for('performance.index') }}">
                                <i class="bi bi-graph-up"></i>
                                تقييم الأداء
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'documents' in request.endpoint }}" href="{{ url_for('documents.index') }}">
                                <i class="bi bi-file-earmark-text"></i>
                                المستندات
                            </a>
                        </li>
                        
                        {% if current_user.role in ['admin', 'hr'] %}
                        <hr class="text-muted">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('dashboard.reports') }}">
                                <i class="bi bi-bar-chart"></i>
                                التقارير
                            </a>
                        </li>
                        {% endif %}
                        
                        {% if current_user.role == 'admin' %}
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'auth.users' in request.endpoint }}" href="{{ url_for('auth.users') }}">
                                <i class="bi bi-person-gear"></i>
                                إدارة المستخدمين
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </nav>
            {% endif %}

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="pt-3 pb-2 mb-3">
                    <!-- Flash Messages -->
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    {% block content %}{% endblock %}
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
