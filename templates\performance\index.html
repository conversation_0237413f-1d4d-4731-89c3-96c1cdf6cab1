{% extends "base.html" %}

{% block title %}تقييم الأداء - نظام إدارة شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-graph-up"></i>
        تقييم الأداء
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('performance.add') }}" class="btn btn-primary">
                <i class="bi bi-plus-lg"></i>
                تقييم جديد
            </a>
            <a href="{{ url_for('performance.reports') }}" class="btn btn-success">
                <i class="bi bi-bar-chart"></i>
                تقارير الأداء
            </a>
        </div>
    </div>
</div>

<!-- Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="employee_id" class="form-label">الموظف</label>
                <select class="form-select" id="employee_id" name="employee_id">
                    <option value="">جميع الموظفين</option>
                    {% for emp in employees %}
                    <option value="{{ emp.id }}" {% if emp.id == employee_id %}selected{% endif %}>
                        {{ emp.employee_id }} - {{ emp.full_name_ar or emp.full_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <label for="period" class="form-label">فترة التقييم</label>
                <input type="text" class="form-control" id="period" name="period" value="{{ period }}" placeholder="مثال: Q1 2024">
            </div>
            <div class="col-md-4">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="bi bi-search"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Performance Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">تقييمات الأداء</h5>
        <span class="badge bg-primary">{{ performances.total }} تقييم</span>
    </div>
    <div class="card-body p-0">
        {% if performances.items %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>الموظف</th>
                        <th>فترة التقييم</th>
                        <th>المقيم</th>
                        <th>النتيجة الإجمالية</th>
                        <th>تاريخ التقييم</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for performance in performances.items %}
                    <tr>
                        <td>
                            <div>
                                <strong>{{ performance.employee.full_name_ar or performance.employee.full_name }}</strong>
                                <br>
                                <small class="text-muted">{{ performance.employee.employee_id }} - {{ performance.employee.department }}</small>
                            </div>
                        </td>
                        <td>{{ performance.evaluation_period }}</td>
                        <td>{{ performance.evaluator }}</td>
                        <td>
                            {% if performance.overall_score %}
                                <div class="d-flex align-items-center">
                                    <span class="me-2">{{ "%.1f"|format(performance.overall_score) }}/10</span>
                                    <div class="progress" style="width: 60px; height: 8px;">
                                        <div class="progress-bar 
                                            {% if performance.overall_score >= 8 %}bg-success
                                            {% elif performance.overall_score >= 6 %}bg-warning
                                            {% else %}bg-danger{% endif %}" 
                                             style="width: {{ (performance.overall_score / 10 * 100) }}%"></div>
                                    </div>
                                </div>
                            {% else %}
                                <span class="text-muted">غير محسوب</span>
                            {% endif %}
                        </td>
                        <td>{{ performance.evaluation_date.strftime('%Y-%m-%d') }}</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('performance.view', id=performance.id) }}" 
                                   class="btn btn-outline-primary" title="عرض">
                                    <i class="bi bi-eye"></i>
                                </a>
                                {% if current_user.role in ['admin', 'hr'] %}
                                <a href="{{ url_for('performance.edit', id=performance.id) }}" 
                                   class="btn btn-outline-secondary" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                {% endif %}
                                <a href="{{ url_for('performance.employee_history', employee_id=performance.employee_id) }}" 
                                   class="btn btn-outline-info" title="تاريخ التقييمات">
                                    <i class="bi bi-clock-history"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-graph-up" style="font-size: 4rem; color: #dee2e6;"></i>
            <h5 class="mt-3 text-muted">لا توجد تقييمات أداء</h5>
            <p class="text-muted">لم يتم العثور على أي تقييمات أداء بناءً على معايير البحث المحددة.</p>
            <a href="{{ url_for('performance.add') }}" class="btn btn-primary">
                <i class="bi bi-plus-lg"></i>
                إضافة تقييم جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Pagination -->
{% if performances.pages > 1 %}
<nav aria-label="تنقل الصفحات" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if performances.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('performance.index', page=performances.prev_num, employee_id=employee_id, period=period) }}">
                السابق
            </a>
        </li>
        {% endif %}
        
        {% for page_num in performances.iter_pages() %}
            {% if page_num %}
                {% if page_num != performances.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('performance.index', page=page_num, employee_id=employee_id, period=period) }}">
                        {{ page_num }}
                    </a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">…</span>
            </li>
            {% endif %}
        {% endfor %}
        
        {% if performances.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('performance.index', page=performances.next_num, employee_id=employee_id, period=period) }}">
                التالي
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

<!-- Performance Summary Cards -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center stats-card">
            <div class="card-body">
                <h4>{{ performances.total }}</h4>
                <small>إجمالي التقييمات</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center stats-card-2">
            <div class="card-body">
                <h4>{{ employees|length }}</h4>
                <small>الموظفون النشطون</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center stats-card-3">
            <div class="card-body">
                <h4>
                    {% set high_performers = performances.items|selectattr('overall_score', 'ge', 8)|list %}
                    {{ high_performers|length }}
                </h4>
                <small>أداء ممتاز (8+)</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center stats-card-4">
            <div class="card-body">
                <h4>
                    {% set avg_score = performances.items|map(attribute='overall_score')|select|average %}
                    {{ "%.1f"|format(avg_score) if avg_score else "0.0" }}
                </h4>
                <small>متوسط النتائج</small>
            </div>
        </div>
    </div>
</div>
{% endblock %}
