from flask import Blueprint, render_template, request, redirect, url_for, flash, current_app
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from models import db, Employee
from datetime import datetime, date
import os
from PIL import Image

employees_bp = Blueprint('employees', __name__)

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

def resize_image(image_path, max_size=(300, 300)):
    """Resize image to maximum dimensions while maintaining aspect ratio"""
    try:
        with Image.open(image_path) as img:
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
            img.save(image_path, optimize=True, quality=85)
    except Exception as e:
        print(f"Error resizing image: {e}")

@employees_bp.route('/')
@login_required
def index():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    department = request.args.get('department', '')
    status = request.args.get('status', 'active')
    
    query = Employee.query
    
    if search:
        query = query.filter(
            db.or_(
                Employee.first_name.contains(search),
                Employee.last_name.contains(search),
                Employee.first_name_ar.contains(search),
                Employee.last_name_ar.contains(search),
                Employee.employee_id.contains(search),
                Employee.email.contains(search)
            )
        )
    
    if department:
        query = query.filter_by(department=department)
    
    if status:
        query = query.filter_by(status=status)
    
    employees = query.order_by(Employee.created_at.desc()).paginate(
        page=page, per_page=10, error_out=False
    )
    
    # Get all departments for filter
    departments = db.session.query(Employee.department).distinct().all()
    departments = [d[0] for d in departments if d[0]]
    
    return render_template('employees/index.html', 
                         employees=employees, 
                         departments=departments,
                         search=search,
                         department=department,
                         status=status)

@employees_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    if current_user.role not in ['admin', 'hr']:
        flash('ليس لديك صلاحية لإضافة موظفين', 'error')
        return redirect(url_for('employees.index'))
    
    if request.method == 'POST':
        # Get form data
        employee_id = request.form.get('employee_id')
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        first_name_ar = request.form.get('first_name_ar')
        last_name_ar = request.form.get('last_name_ar')
        email = request.form.get('email')
        phone = request.form.get('phone')
        national_id = request.form.get('national_id')
        birth_date_str = request.form.get('birth_date')
        hire_date_str = request.form.get('hire_date')
        department = request.form.get('department')
        position = request.form.get('position')
        position_ar = request.form.get('position_ar')
        basic_salary = request.form.get('basic_salary', type=float)
        address = request.form.get('address')
        emergency_contact = request.form.get('emergency_contact')
        emergency_phone = request.form.get('emergency_phone')
        
        # Validate required fields
        if not all([employee_id, first_name, last_name, hire_date_str, department, position]):
            flash('الحقول المطلوبة: رقم الموظف، الاسم الأول، الاسم الأخير، تاريخ التوظيف، القسم، المنصب', 'error')
            return render_template('employees/add.html')
        
        # Check if employee ID already exists
        if Employee.query.filter_by(employee_id=employee_id).first():
            flash('رقم الموظف موجود بالفعل', 'error')
            return render_template('employees/add.html')
        
        # Check if email already exists
        if email and Employee.query.filter_by(email=email).first():
            flash('البريد الإلكتروني موجود بالفعل', 'error')
            return render_template('employees/add.html')
        
        # Parse dates
        try:
            hire_date = datetime.strptime(hire_date_str, '%Y-%m-%d').date()
            birth_date = None
            if birth_date_str:
                birth_date = datetime.strptime(birth_date_str, '%Y-%m-%d').date()
        except ValueError:
            flash('تنسيق التاريخ غير صحيح', 'error')
            return render_template('employees/add.html')
        
        # Handle profile picture upload
        profile_picture = None
        if 'profile_picture' in request.files:
            file = request.files['profile_picture']
            if file and file.filename and allowed_file(file.filename):
                filename = secure_filename(f"{employee_id}_{file.filename}")
                file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'profiles', filename)
                
                # Create profiles directory if it doesn't exist
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                
                file.save(file_path)
                resize_image(file_path)
                profile_picture = f"profiles/{filename}"
        
        # Create new employee
        employee = Employee(
            employee_id=employee_id,
            first_name=first_name,
            last_name=last_name,
            first_name_ar=first_name_ar,
            last_name_ar=last_name_ar,
            email=email,
            phone=phone,
            national_id=national_id,
            birth_date=birth_date,
            hire_date=hire_date,
            department=department,
            position=position,
            position_ar=position_ar,
            basic_salary=basic_salary or 0.0,
            address=address,
            emergency_contact=emergency_contact,
            emergency_phone=emergency_phone,
            profile_picture=profile_picture
        )
        
        try:
            db.session.add(employee)
            db.session.commit()
            flash(f'تم إضافة الموظف {employee.full_name} بنجاح', 'success')
            return redirect(url_for('employees.view', id=employee.id))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إضافة الموظف', 'error')
            print(f"Error adding employee: {e}")
    
    return render_template('employees/add.html')

@employees_bp.route('/<int:id>')
@login_required
def view(id):
    employee = Employee.query.get_or_404(id)
    return render_template('employees/view.html', employee=employee)

@employees_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    if current_user.role not in ['admin', 'hr']:
        flash('ليس لديك صلاحية لتعديل الموظفين', 'error')
        return redirect(url_for('employees.view', id=id))

    employee = Employee.query.get_or_404(id)

    if request.method == 'POST':
        # Update employee data
        employee.first_name = request.form.get('first_name')
        employee.last_name = request.form.get('last_name')
        employee.first_name_ar = request.form.get('first_name_ar')
        employee.last_name_ar = request.form.get('last_name_ar')
        employee.email = request.form.get('email')
        employee.phone = request.form.get('phone')
        employee.national_id = request.form.get('national_id')
        employee.department = request.form.get('department')
        employee.position = request.form.get('position')
        employee.position_ar = request.form.get('position_ar')
        employee.basic_salary = request.form.get('basic_salary', type=float) or 0.0
        employee.address = request.form.get('address')
        employee.emergency_contact = request.form.get('emergency_contact')
        employee.emergency_phone = request.form.get('emergency_phone')
        employee.status = request.form.get('status')

        # Parse dates
        birth_date_str = request.form.get('birth_date')
        hire_date_str = request.form.get('hire_date')

        try:
            if hire_date_str:
                employee.hire_date = datetime.strptime(hire_date_str, '%Y-%m-%d').date()
            if birth_date_str:
                employee.birth_date = datetime.strptime(birth_date_str, '%Y-%m-%d').date()
        except ValueError:
            flash('تنسيق التاريخ غير صحيح', 'error')
            return render_template('employees/edit.html', employee=employee)

        # Handle profile picture upload
        if 'profile_picture' in request.files:
            file = request.files['profile_picture']
            if file and file.filename and allowed_file(file.filename):
                # Delete old profile picture if exists
                if employee.profile_picture:
                    old_file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], employee.profile_picture)
                    if os.path.exists(old_file_path):
                        os.remove(old_file_path)

                filename = secure_filename(f"{employee.employee_id}_{file.filename}")
                file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'profiles', filename)

                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                file.save(file_path)
                resize_image(file_path)
                employee.profile_picture = f"profiles/{filename}"

        employee.updated_at = datetime.utcnow()

        try:
            db.session.commit()
            flash(f'تم تحديث بيانات الموظف {employee.full_name} بنجاح', 'success')
            return redirect(url_for('employees.view', id=employee.id))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء تحديث الموظف', 'error')
            print(f"Error updating employee: {e}")

    return render_template('employees/edit.html', employee=employee)

@employees_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لحذف الموظفين', 'error')
        return redirect(url_for('employees.view', id=id))

    employee = Employee.query.get_or_404(id)

    try:
        # Delete profile picture if exists
        if employee.profile_picture:
            file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], employee.profile_picture)
            if os.path.exists(file_path):
                os.remove(file_path)

        db.session.delete(employee)
        db.session.commit()
        flash(f'تم حذف الموظف {employee.full_name} بنجاح', 'success')
        return redirect(url_for('employees.index'))
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء حذف الموظف', 'error')
        print(f"Error deleting employee: {e}")
        return redirect(url_for('employees.view', id=id))
