from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_required, current_user
from models import db, Employee, Leave
from datetime import datetime, date

leaves_bp = Blueprint('leaves', __name__)

@leaves_bp.route('/')
@login_required
def index():
    page = request.args.get('page', 1, type=int)
    employee_id = request.args.get('employee_id', type=int)
    leave_type = request.args.get('leave_type', '')
    status = request.args.get('status', '')
    
    query = Leave.query
    
    if employee_id:
        query = query.filter_by(employee_id=employee_id)
    
    if leave_type:
        query = query.filter_by(leave_type=leave_type)
    
    if status:
        query = query.filter_by(status=status)
    
    leaves = query.order_by(Leave.created_at.desc()).paginate(
        page=page, per_page=10, error_out=False
    )
    
    # Get all employees for filter
    employees = Employee.query.filter_by(status='active').all()
    
    return render_template('leaves/index.html',
                         leaves=leaves,
                         employees=employees,
                         employee_id=employee_id,
                         leave_type=leave_type,
                         status=status)

@leaves_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    if request.method == 'POST':
        employee_id = request.form.get('employee_id', type=int)
        leave_type = request.form.get('leave_type')
        start_date_str = request.form.get('start_date')
        end_date_str = request.form.get('end_date')
        reason = request.form.get('reason')
        
        if not all([employee_id, leave_type, start_date_str, end_date_str]):
            flash('جميع الحقول مطلوبة', 'error')
            return render_template('leaves/add.html', employees=Employee.query.filter_by(status='active').all())
        
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            flash('تنسيق التاريخ غير صحيح', 'error')
            return render_template('leaves/add.html', employees=Employee.query.filter_by(status='active').all())
        
        if start_date > end_date:
            flash('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'error')
            return render_template('leaves/add.html', employees=Employee.query.filter_by(status='active').all())
        
        # Calculate days count
        days_count = (end_date - start_date).days + 1
        
        # Create new leave
        leave = Leave(
            employee_id=employee_id,
            leave_type=leave_type,
            start_date=start_date,
            end_date=end_date,
            days_count=days_count,
            reason=reason
        )
        
        try:
            db.session.add(leave)
            db.session.commit()
            flash(f'تم إضافة طلب الإجازة للموظف {leave.employee.full_name} بنجاح', 'success')
            return redirect(url_for('leaves.view', id=leave.id))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إضافة طلب الإجازة', 'error')
            print(f"Error adding leave: {e}")
    
    employees = Employee.query.filter_by(status='active').all()
    return render_template('leaves/add.html', employees=employees)

@leaves_bp.route('/<int:id>')
@login_required
def view(id):
    leave = Leave.query.get_or_404(id)
    return render_template('leaves/view.html', leave=leave)

@leaves_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    if current_user.role not in ['admin', 'hr']:
        flash('ليس لديك صلاحية لتعديل الإجازات', 'error')
        return redirect(url_for('leaves.view', id=id))
    
    leave = Leave.query.get_or_404(id)
    
    if request.method == 'POST':
        leave.leave_type = request.form.get('leave_type')
        leave.reason = request.form.get('reason')
        leave.status = request.form.get('status')
        leave.notes = request.form.get('notes')
        
        start_date_str = request.form.get('start_date')
        end_date_str = request.form.get('end_date')
        
        try:
            if start_date_str:
                leave.start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            if end_date_str:
                leave.end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
            
            if leave.start_date and leave.end_date:
                leave.days_count = (leave.end_date - leave.start_date).days + 1
        except ValueError:
            flash('تنسيق التاريخ غير صحيح', 'error')
            return render_template('leaves/edit.html', leave=leave)
        
        # Set approval info if status changed to approved
        if leave.status == 'approved' and not leave.approved_by:
            leave.approved_by = current_user.username
            leave.approved_date = date.today()
        
        try:
            db.session.commit()
            flash(f'تم تحديث طلب الإجازة للموظف {leave.employee.full_name} بنجاح', 'success')
            return redirect(url_for('leaves.view', id=leave.id))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء تحديث طلب الإجازة', 'error')
            print(f"Error updating leave: {e}")
    
    return render_template('leaves/edit.html', leave=leave)

@leaves_bp.route('/<int:id>/approve', methods=['POST'])
@login_required
def approve(id):
    if current_user.role not in ['admin', 'hr']:
        flash('ليس لديك صلاحية لاعتماد الإجازات', 'error')
        return redirect(url_for('leaves.view', id=id))
    
    leave = Leave.query.get_or_404(id)
    
    if leave.status != 'pending':
        flash('لا يمكن اعتماد هذا الطلب', 'error')
        return redirect(url_for('leaves.view', id=id))
    
    leave.status = 'approved'
    leave.approved_by = current_user.username
    leave.approved_date = date.today()
    
    try:
        db.session.commit()
        flash(f'تم اعتماد طلب الإجازة للموظف {leave.employee.full_name}', 'success')
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء اعتماد الطلب', 'error')
    
    return redirect(url_for('leaves.view', id=id))

@leaves_bp.route('/<int:id>/reject', methods=['POST'])
@login_required
def reject(id):
    if current_user.role not in ['admin', 'hr']:
        flash('ليس لديك صلاحية لرفض الإجازات', 'error')
        return redirect(url_for('leaves.view', id=id))
    
    leave = Leave.query.get_or_404(id)
    
    if leave.status != 'pending':
        flash('لا يمكن رفض هذا الطلب', 'error')
        return redirect(url_for('leaves.view', id=id))
    
    leave.status = 'rejected'
    leave.approved_by = current_user.username
    leave.approved_date = date.today()
    leave.notes = request.form.get('rejection_reason', '')
    
    try:
        db.session.commit()
        flash(f'تم رفض طلب الإجازة للموظف {leave.employee.full_name}', 'info')
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء رفض الطلب', 'error')
    
    return redirect(url_for('leaves.view', id=id))

@leaves_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لحذف الإجازات', 'error')
        return redirect(url_for('leaves.view', id=id))
    
    leave = Leave.query.get_or_404(id)
    
    try:
        db.session.delete(leave)
        db.session.commit()
        flash(f'تم حذف طلب الإجازة للموظف {leave.employee.full_name} بنجاح', 'success')
        return redirect(url_for('leaves.index'))
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء حذف طلب الإجازة', 'error')
        return redirect(url_for('leaves.view', id=id))

@leaves_bp.route('/calendar')
@login_required
def calendar():
    # Get all approved leaves for calendar view
    leaves = Leave.query.filter_by(status='approved').all()
    
    # Format leaves for calendar
    calendar_events = []
    for leave in leaves:
        calendar_events.append({
            'title': f"{leave.employee.full_name} - {leave.leave_type}",
            'start': leave.start_date.isoformat(),
            'end': (leave.end_date).isoformat(),
            'color': get_leave_color(leave.leave_type),
            'url': url_for('leaves.view', id=leave.id)
        })
    
    return render_template('leaves/calendar.html', events=calendar_events)

def get_leave_color(leave_type):
    colors = {
        'annual': '#007bff',
        'sick': '#dc3545',
        'emergency': '#ffc107',
        'maternity': '#28a745'
    }
    return colors.get(leave_type, '#6c757d')
