# Employee routes
@app.route('/employees')
@login_required
def employees():
    employees = Employee.query.all()
    
    employees_html = ""
    if employees:
        for emp in employees:
            employees_html += f"""
            <tr>
                <td><strong>{emp.employee_id}</strong></td>
                <td>{emp.full_name_ar or emp.full_name}</td>
                <td>{emp.department or '-'}</td>
                <td>{emp.position or '-'}</td>
                <td>{emp.basic_salary:,.0f} ريال</td>
                <td>{emp.hire_date.strftime('%Y-%m-%d')}</td>
                <td><span class="badge bg-success">{emp.status}</span></td>
            </tr>
            """
    else:
        employees_html = '<tr><td colspan="7" class="text-center">لا توجد موظفون</td></tr>'
    
    content = f"""
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2">
            <i class="bi bi-people"></i> إدارة الموظفين
        </h1>
        <a href="/employees/add" class="btn btn-primary">
            <i class="bi bi-person-plus"></i> إضافة موظف جديد
        </a>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">قائمة الموظفين ({len(employees)})</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>رقم الموظف</th>
                            <th>الاسم</th>
                            <th>القسم</th>
                            <th>المنصب</th>
                            <th>الراتب الأساسي</th>
                            <th>تاريخ التوظيف</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {employees_html}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    """
    
    return render_template_string(BASE_TEMPLATE, 
                                title="الموظفون",
                                content=content,
                                active_page="employees")

@app.route('/employees/add', methods=['GET', 'POST'])
@login_required
def add_employee():
    if request.method == 'POST':
        employee_id = request.form.get('employee_id')
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        first_name_ar = request.form.get('first_name_ar')
        last_name_ar = request.form.get('last_name_ar')
        email = request.form.get('email')
        phone = request.form.get('phone')
        department = request.form.get('department')
        position = request.form.get('position')
        basic_salary = request.form.get('basic_salary', type=float) or 0.0
        hire_date_str = request.form.get('hire_date')
        
        if not all([employee_id, first_name, last_name, department, position]):
            flash('الحقول المطلوبة: رقم الموظف، الاسم الأول، الاسم الأخير، القسم، المنصب', 'error')
        elif Employee.query.filter_by(employee_id=employee_id).first():
            flash('رقم الموظف موجود بالفعل', 'error')
        else:
            try:
                hire_date = datetime.strptime(hire_date_str, '%Y-%m-%d').date() if hire_date_str else date.today()
                
                employee = Employee(
                    employee_id=employee_id,
                    first_name=first_name,
                    last_name=last_name,
                    first_name_ar=first_name_ar,
                    last_name_ar=last_name_ar,
                    email=email,
                    phone=phone,
                    department=department,
                    position=position,
                    basic_salary=basic_salary,
                    hire_date=hire_date
                )
                
                db.session.add(employee)
                db.session.commit()
                flash(f'تم إضافة الموظف {employee.full_name} بنجاح', 'success')
                return redirect(url_for('employees'))
            except Exception as e:
                db.session.rollback()
                flash('حدث خطأ أثناء إضافة الموظف', 'error')
    
    content = """
    <h1 class="h2 mb-4">
        <i class="bi bi-person-plus"></i> إضافة موظف جديد
    </h1>

    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">بيانات الموظف</h5>
        </div>
        <div class="card-body">
            <form method="POST">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">رقم الموظف *</label>
                            <input type="text" class="form-control" name="employee_id" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" name="email">
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">الاسم الأول (إنجليزي) *</label>
                            <input type="text" class="form-control" name="first_name" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">الاسم الأخير (إنجليزي) *</label>
                            <input type="text" class="form-control" name="last_name" required>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">الاسم الأول (عربي)</label>
                            <input type="text" class="form-control" name="first_name_ar">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">الاسم الأخير (عربي)</label>
                            <input type="text" class="form-control" name="last_name_ar">
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" name="phone">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">تاريخ التوظيف</label>
                            <input type="date" class="form-control" name="hire_date">
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">القسم *</label>
                            <input type="text" class="form-control" name="department" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">المنصب *</label>
                            <input type="text" class="form-control" name="position" required>
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">الراتب الأساسي</label>
                    <input type="number" class="form-control" name="basic_salary" step="0.01" min="0">
                </div>
                
                <div class="d-flex justify-content-between">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-lg"></i> حفظ الموظف
                    </button>
                    <a href="/employees" class="btn btn-secondary">
                        <i class="bi bi-x-lg"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
    """
    
    return render_template_string(BASE_TEMPLATE, 
                                title="إضافة موظف",
                                content=content,
                                active_page="employees")

# Payroll routes
@app.route('/payroll')
@login_required
def payroll():
    payrolls = Payroll.query.order_by(Payroll.created_at.desc()).all()
    
    payrolls_html = ""
    if payrolls:
        for payroll in payrolls:
            payrolls_html += f"""
            <tr>
                <td>
                    <div>
                        <strong>{payroll.employee.full_name_ar or payroll.employee.full_name}</strong>
                        <br>
                        <small class="text-muted">{payroll.employee.employee_id}</small>
                    </div>
                </td>
                <td>{payroll.month}/{payroll.year}</td>
                <td>{payroll.basic_salary:,.0f} ريال</td>
                <td>{payroll.allowances:,.0f} ريال</td>
                <td>{payroll.deductions:,.0f} ريال</td>
                <td><strong>{payroll.net_salary:,.0f} ريال</strong></td>
                <td><span class="badge bg-success">{payroll.status}</span></td>
            </tr>
            """
    else:
        payrolls_html = '<tr><td colspan="7" class="text-center">لا توجد كشوف رواتب</td></tr>'
    
    content = f"""
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2">
            <i class="bi bi-cash-stack"></i> إدارة الرواتب
        </h1>
        <a href="/payroll/generate" class="btn btn-primary">
            <i class="bi bi-plus-lg"></i> إنشاء كشف راتب
        </a>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">كشوف الرواتب ({len(payrolls)})</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>الموظف</th>
                            <th>الشهر/السنة</th>
                            <th>الراتب الأساسي</th>
                            <th>البدلات</th>
                            <th>الخصومات</th>
                            <th>صافي الراتب</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {payrolls_html}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    """
    
    return render_template_string(BASE_TEMPLATE, 
                                title="الرواتب",
                                content=content,
                                active_page="payroll")
