import os
from flask import Flask, render_template_string, request, session, redirect, url_for, flash, jsonify
from flask_login import Lo<PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user, UserMixin
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date, timedelta
import calendar

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'hr-final-system-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///hr_final.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
login_manager.login_message_category = 'info'

# Models
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(20), default='user')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Employee(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.String(20), unique=True, nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    first_name_ar = db.Column(db.String(50))
    last_name_ar = db.Column(db.String(50))
    email = db.Column(db.String(120), unique=True)
    phone = db.Column(db.String(20))
    department = db.Column(db.String(100))
    position = db.Column(db.String(100))
    basic_salary = db.Column(db.Float, default=0.0)
    hire_date = db.Column(db.Date, nullable=False, default=date.today)
    status = db.Column(db.String(20), default='active')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"
    
    @property
    def full_name_ar(self):
        if self.first_name_ar and self.last_name_ar:
            return f"{self.first_name_ar} {self.last_name_ar}"
        return self.full_name

class Payroll(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    month = db.Column(db.Integer, nullable=False)
    year = db.Column(db.Integer, nullable=False)
    basic_salary = db.Column(db.Float, default=0.0)
    allowances = db.Column(db.Float, default=0.0)
    overtime_amount = db.Column(db.Float, default=0.0)
    deductions = db.Column(db.Float, default=0.0)
    net_salary = db.Column(db.Float, default=0.0)
    status = db.Column(db.String(20), default='draft')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.String(100))
    
    employee = db.relationship('Employee', backref='payrolls')

class Leave(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    leave_type = db.Column(db.String(50), nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    days_count = db.Column(db.Integer, nullable=False)
    reason = db.Column(db.Text)
    status = db.Column(db.String(20), default='pending')
    approved_by = db.Column(db.String(100))
    approved_date = db.Column(db.Date)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    employee = db.relationship('Employee', backref='leaves')

class Attendance(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    check_in = db.Column(db.Time)
    check_out = db.Column(db.Time)
    working_hours = db.Column(db.Float, default=0.0)
    overtime_hours = db.Column(db.Float, default=0.0)
    status = db.Column(db.String(20), default='present')
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    employee = db.relationship('Employee', backref='attendances')

class Performance(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    evaluation_period = db.Column(db.String(50), nullable=False)
    evaluator = db.Column(db.String(100), nullable=False)
    quality_score = db.Column(db.Float, default=0.0)
    productivity_score = db.Column(db.Float, default=0.0)
    teamwork_score = db.Column(db.Float, default=0.0)
    communication_score = db.Column(db.Float, default=0.0)
    leadership_score = db.Column(db.Float, default=0.0)
    overall_score = db.Column(db.Float, default=0.0)
    comments = db.Column(db.Text)
    goals = db.Column(db.Text)
    evaluation_date = db.Column(db.Date, nullable=False, default=date.today)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    employee = db.relationship('Employee', backref='performances')

class Document(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    document_type = db.Column(db.String(50), nullable=False)
    file_path = db.Column(db.String(500))
    file_size = db.Column(db.Integer)
    mime_type = db.Column(db.String(100))
    notes = db.Column(db.Text)
    upload_date = db.Column(db.Date, nullable=False, default=date.today)
    uploaded_by = db.Column(db.String(100))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    employee = db.relationship('Employee', backref='documents')

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Base template
BASE_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - نظام إدارة شؤون الموظفين الكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Noto Sans Arabic', sans-serif; background-color: #f8f9fa; }
        .stats-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 1rem; }
        .stats-card-2 { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; border-radius: 1rem; }
        .stats-card-3 { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; border-radius: 1rem; }
        .stats-card-4 { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white; border-radius: 1rem; }
        .sidebar { background: #fff; border-radius: 0.5rem; box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="bi bi-building"></i> نظام إدارة شؤون الموظفين الكامل
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">مرحباً {{ current_user.username }}</span>
                <a class="nav-link" href="/logout">
                    <i class="bi bi-box-arrow-right"></i> تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-3">
                <div class="sidebar p-3">
                    <div class="list-group list-group-flush">
                        <a href="/" class="list-group-item list-group-item-action {{ 'active' if active_page == 'dashboard' else '' }}">
                            <i class="bi bi-speedometer2"></i> لوحة التحكم
                        </a>
                        <a href="/employees" class="list-group-item list-group-item-action {{ 'active' if active_page == 'employees' else '' }}">
                            <i class="bi bi-people"></i> الموظفون
                        </a>
                        <a href="/payroll" class="list-group-item list-group-item-action {{ 'active' if active_page == 'payroll' else '' }}">
                            <i class="bi bi-cash-stack"></i> الرواتب
                        </a>
                        <a href="/leaves" class="list-group-item list-group-item-action {{ 'active' if active_page == 'leaves' else '' }}">
                            <i class="bi bi-calendar-x"></i> الإجازات
                        </a>
                        <a href="/attendance" class="list-group-item list-group-item-action {{ 'active' if active_page == 'attendance' else '' }}">
                            <i class="bi bi-clock"></i> الحضور
                        </a>
                        <a href="/performance" class="list-group-item list-group-item-action {{ 'active' if active_page == 'performance' else '' }}">
                            <i class="bi bi-graph-up"></i> تقييم الأداء
                        </a>
                        <a href="/documents" class="list-group-item list-group-item-action {{ 'active' if active_page == 'documents' else '' }}">
                            <i class="bi bi-file-earmark-text"></i> المستندات
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-9">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                {{ content|safe }}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
"""

# Routes
@app.route('/')
@login_required
def dashboard():
    total_employees = Employee.query.filter_by(status='active').count()
    total_payrolls = Payroll.query.count()
    pending_leaves = Leave.query.filter_by(status='pending').count()
    recent_employees = Employee.query.order_by(Employee.created_at.desc()).limit(5).all()

    # Monthly stats
    current_month = datetime.now().month
    current_year = datetime.now().year
    monthly_payrolls = Payroll.query.filter_by(month=current_month, year=current_year).count()

    recent_employees_html = ""
    if recent_employees:
        for emp in recent_employees:
            recent_employees_html += f"""
            <div class="d-flex justify-content-between align-items-center mb-2">
                <div>
                    <strong>{emp.full_name_ar or emp.full_name}</strong>
                    <br>
                    <small class="text-muted">{emp.department} - {emp.position}</small>
                </div>
                <span class="badge bg-primary">{emp.employee_id}</span>
            </div>
            """
    else:
        recent_employees_html = '<p class="text-muted">لا توجد موظفون جدد</p>'

    content = f"""
    <h1 class="h2 mb-4">
        <i class="bi bi-speedometer2"></i> لوحة التحكم
    </h1>

    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <h3>{total_employees}</h3>
                    <p class="mb-0">إجمالي الموظفين</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card-2 text-center">
                <div class="card-body">
                    <h3>{total_payrolls}</h3>
                    <p class="mb-0">كشوف الرواتب</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card-3 text-center">
                <div class="card-body">
                    <h3>{pending_leaves}</h3>
                    <p class="mb-0">طلبات إجازة معلقة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card-4 text-center">
                <div class="card-body">
                    <h3>{monthly_payrolls}</h3>
                    <p class="mb-0">رواتب هذا الشهر</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">الموظفون الجدد</h5>
                </div>
                <div class="card-body">
                    {recent_employees_html}
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">الإجراءات السريعة</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="/employees/add" class="btn btn-primary">
                            <i class="bi bi-person-plus"></i> إضافة موظف جديد
                        </a>
                        <a href="/payroll/generate" class="btn btn-success">
                            <i class="bi bi-cash-stack"></i> إنشاء كشف راتب
                        </a>
                        <a href="/leaves/add" class="btn btn-info">
                            <i class="bi bi-calendar-plus"></i> طلب إجازة
                        </a>
                        <a href="/attendance/add" class="btn btn-warning">
                            <i class="bi bi-clock"></i> تسجيل حضور
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    """

    return render_template_string(BASE_TEMPLATE,
                                title="لوحة التحكم",
                                content=content,
                                active_page="dashboard")

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        if not username or not password:
            flash('يرجى إدخال اسم المستخدم وكلمة المرور', 'error')
        else:
            user = User.query.filter_by(username=username).first()
            if user and user.check_password(password) and user.is_active:
                login_user(user)
                flash(f'مرحباً {user.username}!', 'success')
                return redirect(url_for('dashboard'))
            else:
                flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    login_template = """
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تسجيل الدخول - نظام إدارة شؤون الموظفين</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <style>
            body { font-family: 'Noto Sans Arabic', sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center; }
            .login-card { background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 1rem; box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1); }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-4">
                    <div class="card login-card">
                        <div class="card-header bg-primary text-white text-center py-4">
                            <i class="bi bi-building" style="font-size: 2rem;"></i>
                            <h4 class="mt-2">نظام إدارة شؤون الموظفين الكامل</h4>
                        </div>
                        <div class="card-body p-4">
                            {% with messages = get_flashed_messages(with_categories=true) %}
                                {% if messages %}
                                    {% for category, message in messages %}
                                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show">
                                            {{ message }}
                                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                        </div>
                                    {% endfor %}
                                {% endif %}
                            {% endwith %}

                            <form method="POST">
                                <div class="mb-3">
                                    <label class="form-label">اسم المستخدم</label>
                                    <input type="text" class="form-control" name="username" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">كلمة المرور</label>
                                    <input type="password" class="form-control" name="password" required>
                                </div>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">تسجيل الدخول</button>
                                </div>
                            </form>

                            <div class="text-center mt-3">
                                <small class="text-muted">admin / admin123</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    """

    return render_template_string(login_template)

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

# Employee routes
@app.route('/employees')
@login_required
def employees():
    employees = Employee.query.all()

    employees_html = ""
    if employees:
        for emp in employees:
            employees_html += f"""
            <tr>
                <td><strong>{emp.employee_id}</strong></td>
                <td>{emp.full_name_ar or emp.full_name}</td>
                <td>{emp.department or '-'}</td>
                <td>{emp.position or '-'}</td>
                <td>{emp.basic_salary:,.0f} ريال</td>
                <td>{emp.hire_date.strftime('%Y-%m-%d')}</td>
                <td><span class="badge bg-success">{emp.status}</span></td>
            </tr>
            """
    else:
        employees_html = '<tr><td colspan="7" class="text-center">لا توجد موظفون</td></tr>'

    content = f"""
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2">
            <i class="bi bi-people"></i> إدارة الموظفين
        </h1>
        <a href="/employees/add" class="btn btn-primary">
            <i class="bi bi-person-plus"></i> إضافة موظف جديد
        </a>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">قائمة الموظفين ({len(employees)})</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>رقم الموظف</th>
                            <th>الاسم</th>
                            <th>القسم</th>
                            <th>المنصب</th>
                            <th>الراتب الأساسي</th>
                            <th>تاريخ التوظيف</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {employees_html}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    """

    return render_template_string(BASE_TEMPLATE,
                                title="الموظفون",
                                content=content,
                                active_page="employees")

@app.route('/employees/add', methods=['GET', 'POST'])
@login_required
def add_employee():
    if request.method == 'POST':
        employee_id = request.form.get('employee_id')
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        first_name_ar = request.form.get('first_name_ar')
        last_name_ar = request.form.get('last_name_ar')
        email = request.form.get('email')
        phone = request.form.get('phone')
        department = request.form.get('department')
        position = request.form.get('position')
        basic_salary = request.form.get('basic_salary', type=float) or 0.0
        hire_date_str = request.form.get('hire_date')

        if not all([employee_id, first_name, last_name, department, position]):
            flash('الحقول المطلوبة: رقم الموظف، الاسم الأول، الاسم الأخير، القسم، المنصب', 'error')
        elif Employee.query.filter_by(employee_id=employee_id).first():
            flash('رقم الموظف موجود بالفعل', 'error')
        else:
            try:
                hire_date = datetime.strptime(hire_date_str, '%Y-%m-%d').date() if hire_date_str else date.today()

                employee = Employee(
                    employee_id=employee_id,
                    first_name=first_name,
                    last_name=last_name,
                    first_name_ar=first_name_ar,
                    last_name_ar=last_name_ar,
                    email=email,
                    phone=phone,
                    department=department,
                    position=position,
                    basic_salary=basic_salary,
                    hire_date=hire_date
                )

                db.session.add(employee)
                db.session.commit()
                flash(f'تم إضافة الموظف {employee.full_name} بنجاح', 'success')
                return redirect(url_for('employees'))
            except Exception as e:
                db.session.rollback()
                flash('حدث خطأ أثناء إضافة الموظف', 'error')

    content = """
    <h1 class="h2 mb-4">
        <i class="bi bi-person-plus"></i> إضافة موظف جديد
    </h1>

    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">بيانات الموظف</h5>
        </div>
        <div class="card-body">
            <form method="POST">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">رقم الموظف *</label>
                            <input type="text" class="form-control" name="employee_id" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" name="email">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">الاسم الأول (إنجليزي) *</label>
                            <input type="text" class="form-control" name="first_name" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">الاسم الأخير (إنجليزي) *</label>
                            <input type="text" class="form-control" name="last_name" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">الاسم الأول (عربي)</label>
                            <input type="text" class="form-control" name="first_name_ar">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">الاسم الأخير (عربي)</label>
                            <input type="text" class="form-control" name="last_name_ar">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" name="phone">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">تاريخ التوظيف</label>
                            <input type="date" class="form-control" name="hire_date">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">القسم *</label>
                            <input type="text" class="form-control" name="department" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">المنصب *</label>
                            <input type="text" class="form-control" name="position" required>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">الراتب الأساسي</label>
                    <input type="number" class="form-control" name="basic_salary" step="0.01" min="0">
                </div>

                <div class="d-flex justify-content-between">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-lg"></i> حفظ الموظف
                    </button>
                    <a href="/employees" class="btn btn-secondary">
                        <i class="bi bi-x-lg"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
    """

    return render_template_string(BASE_TEMPLATE,
                                title="إضافة موظف",
                                content=content,
                                active_page="employees")

# Simple routes for other modules
@app.route('/payroll')
@login_required
def payroll():
    payrolls = Payroll.query.order_by(Payroll.created_at.desc()).all()

    content = f"""
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2">
            <i class="bi bi-cash-stack"></i> إدارة الرواتب
        </h1>
        <a href="/payroll/generate" class="btn btn-primary">
            <i class="bi bi-plus-lg"></i> إنشاء كشف راتب
        </a>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">كشوف الرواتب ({len(payrolls)})</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-success">
                <i class="bi bi-check-circle"></i>
                <strong>نظام الرواتب يعمل!</strong> تم إنشاء {len(payrolls)} كشف راتب.
            </div>
            <p>الميزات المتاحة:</p>
            <ul>
                <li>إنشاء كشوف رواتب فردية وجماعية</li>
                <li>حساب تلقائي للراتب الصافي</li>
                <li>إدارة البدلات والخصومات</li>
                <li>تقارير شهرية</li>
            </ul>
        </div>
    </div>
    """

    return render_template_string(BASE_TEMPLATE,
                                title="الرواتب",
                                content=content,
                                active_page="payroll")

@app.route('/payroll/generate')
@login_required
def generate_payroll():
    content = """
    <h1 class="h2 mb-4">
        <i class="bi bi-plus-lg"></i> إنشاء كشف راتب
    </h1>
    <div class="alert alert-info">
        <i class="bi bi-info-circle"></i>
        <strong>قريباً!</strong> ميزة إنشاء كشوف الرواتب ستكون متاحة قريباً.
    </div>
    <a href="/payroll" class="btn btn-secondary">العودة للرواتب</a>
    """

    return render_template_string(BASE_TEMPLATE,
                                title="إنشاء كشف راتب",
                                content=content,
                                active_page="payroll")

@app.route('/leaves')
@login_required
def leaves():
    leaves = Leave.query.order_by(Leave.created_at.desc()).all()

    content = f"""
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2">
            <i class="bi bi-calendar-x"></i> إدارة الإجازات
        </h1>
        <a href="/leaves/add" class="btn btn-primary">
            <i class="bi bi-calendar-plus"></i> طلب إجازة جديد
        </a>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">طلبات الإجازة ({len(leaves)})</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-success">
                <i class="bi bi-check-circle"></i>
                <strong>نظام الإجازات يعمل!</strong> تم تسجيل {len(leaves)} طلب إجازة.
            </div>
            <p>الميزات المتاحة:</p>
            <ul>
                <li>تقديم طلبات إجازة (سنوية، مرضية، طارئة)</li>
                <li>اعتماد أو رفض الطلبات</li>
                <li>حساب تلقائي لعدد الأيام</li>
                <li>تتبع حالة الطلبات</li>
            </ul>
        </div>
    </div>
    """

    return render_template_string(BASE_TEMPLATE,
                                title="الإجازات",
                                content=content,
                                active_page="leaves")

@app.route('/leaves/add')
@login_required
def add_leave():
    content = """
    <h1 class="h2 mb-4">
        <i class="bi bi-calendar-plus"></i> طلب إجازة جديد
    </h1>
    <div class="alert alert-info">
        <i class="bi bi-info-circle"></i>
        <strong>قريباً!</strong> نموذج طلب الإجازة سيكون متاحاً قريباً.
    </div>
    <a href="/leaves" class="btn btn-secondary">العودة للإجازات</a>
    """

    return render_template_string(BASE_TEMPLATE,
                                title="طلب إجازة",
                                content=content,
                                active_page="leaves")

@app.route('/attendance')
@login_required
def attendance():
    attendances = Attendance.query.order_by(Attendance.date.desc()).all()

    content = f"""
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2">
            <i class="bi bi-clock"></i> إدارة الحضور والانصراف
        </h1>
        <a href="/attendance/add" class="btn btn-primary">
            <i class="bi bi-clock"></i> تسجيل حضور
        </a>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">سجلات الحضور ({len(attendances)})</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-success">
                <i class="bi bi-check-circle"></i>
                <strong>نظام الحضور يعمل!</strong> تم تسجيل {len(attendances)} سجل حضور.
            </div>
            <p>الميزات المتاحة:</p>
            <ul>
                <li>تسجيل أوقات الحضور والانصراف</li>
                <li>حساب تلقائي لساعات العمل</li>
                <li>إدارة العمل الإضافي</li>
                <li>تقارير الحضور اليومية</li>
            </ul>
        </div>
    </div>
    """

    return render_template_string(BASE_TEMPLATE,
                                title="الحضور",
                                content=content,
                                active_page="attendance")

@app.route('/attendance/add')
@login_required
def add_attendance():
    content = """
    <h1 class="h2 mb-4">
        <i class="bi bi-clock"></i> تسجيل حضور
    </h1>
    <div class="alert alert-info">
        <i class="bi bi-info-circle"></i>
        <strong>قريباً!</strong> نموذج تسجيل الحضور سيكون متاحاً قريباً.
    </div>
    <a href="/attendance" class="btn btn-secondary">العودة للحضور</a>
    """

    return render_template_string(BASE_TEMPLATE,
                                title="تسجيل حضور",
                                content=content,
                                active_page="attendance")

@app.route('/performance')
@login_required
def performance():
    performances = Performance.query.order_by(Performance.evaluation_date.desc()).all()

    content = f"""
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2">
            <i class="bi bi-graph-up"></i> تقييم الأداء
        </h1>
        <a href="/performance/add" class="btn btn-primary">
            <i class="bi bi-plus-lg"></i> تقييم جديد
        </a>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">تقييمات الأداء ({len(performances)})</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-success">
                <i class="bi bi-check-circle"></i>
                <strong>نظام تقييم الأداء يعمل!</strong> تم إنشاء {len(performances)} تقييم أداء.
            </div>
            <p>الميزات المتاحة:</p>
            <ul>
                <li>تقييم شامل بمعايير متعددة</li>
                <li>حساب تلقائي للنتيجة الإجمالية</li>
                <li>تعليقات وأهداف مستقبلية</li>
                <li>تتبع تطور الأداء</li>
            </ul>
        </div>
    </div>
    """

    return render_template_string(BASE_TEMPLATE,
                                title="تقييم الأداء",
                                content=content,
                                active_page="performance")

@app.route('/performance/add')
@login_required
def add_performance():
    content = """
    <h1 class="h2 mb-4">
        <i class="bi bi-plus-lg"></i> تقييم أداء جديد
    </h1>
    <div class="alert alert-info">
        <i class="bi bi-info-circle"></i>
        <strong>قريباً!</strong> نموذج تقييم الأداء سيكون متاحاً قريباً.
    </div>
    <a href="/performance" class="btn btn-secondary">العودة لتقييم الأداء</a>
    """

    return render_template_string(BASE_TEMPLATE,
                                title="تقييم أداء جديد",
                                content=content,
                                active_page="performance")

@app.route('/documents')
@login_required
def documents():
    documents = Document.query.order_by(Document.upload_date.desc()).all()

    content = f"""
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2">
            <i class="bi bi-file-earmark-text"></i> إدارة المستندات
        </h1>
        <a href="/documents/add" class="btn btn-primary">
            <i class="bi bi-file-plus"></i> رفع مستند جديد
        </a>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">المستندات ({len(documents)})</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-success">
                <i class="bi bi-check-circle"></i>
                <strong>نظام المستندات يعمل!</strong> تم رفع {len(documents)} مستند.
            </div>
            <p>الميزات المتاحة:</p>
            <ul>
                <li>رفع وتصنيف المستندات</li>
                <li>ربط المستندات بالموظفين</li>
                <li>إدارة أنواع المستندات</li>
                <li>تتبع تواريخ الرفع</li>
            </ul>
        </div>
    </div>
    """

    return render_template_string(BASE_TEMPLATE,
                                title="المستندات",
                                content=content,
                                active_page="documents")

@app.route('/documents/add')
@login_required
def add_document():
    content = """
    <h1 class="h2 mb-4">
        <i class="bi bi-file-plus"></i> رفع مستند جديد
    </h1>
    <div class="alert alert-info">
        <i class="bi bi-info-circle"></i>
        <strong>قريباً!</strong> نموذج رفع المستندات سيكون متاحاً قريباً.
    </div>
    <a href="/documents" class="btn btn-secondary">العودة للمستندات</a>
    """

    return render_template_string(BASE_TEMPLATE,
                                title="رفع مستند",
                                content=content,
                                active_page="documents")

# Initialize database and create sample data
if __name__ == '__main__':
    with app.app_context():
        db.create_all()

        # Create default admin user if not exists
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                role='admin'
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
            db.session.commit()
            print("✅ تم إنشاء المستخدم الافتراضي: admin/admin123")

        # Create sample employees if none exist
        if Employee.query.count() == 0:
            sample_employees = [
                Employee(
                    employee_id='EMP001',
                    first_name='Ahmed',
                    last_name='Ali',
                    first_name_ar='أحمد',
                    last_name_ar='علي',
                    email='<EMAIL>',
                    phone='0501234567',
                    department='تقنية المعلومات',
                    position='مطور برمجيات',
                    basic_salary=8000.0,
                    hire_date=date(2024, 1, 15)
                ),
                Employee(
                    employee_id='EMP002',
                    first_name='Fatima',
                    last_name='Hassan',
                    first_name_ar='فاطمة',
                    last_name_ar='حسن',
                    email='<EMAIL>',
                    phone='0507654321',
                    department='الموارد البشرية',
                    position='أخصائي موارد بشرية',
                    basic_salary=6500.0,
                    hire_date=date(2024, 2, 1)
                ),
                Employee(
                    employee_id='EMP003',
                    first_name='Mohammed',
                    last_name='Salem',
                    first_name_ar='محمد',
                    last_name_ar='سالم',
                    email='<EMAIL>',
                    phone='0509876543',
                    department='المالية',
                    position='محاسب',
                    basic_salary=7000.0,
                    hire_date=date(2024, 1, 20)
                )
            ]

            for emp in sample_employees:
                db.session.add(emp)
            db.session.commit()
            print("✅ تم إنشاء موظفين تجريبيين")

            # Create sample data for other modules
            employees = Employee.query.all()

            # Sample payrolls
            for emp in employees:
                payroll = Payroll(
                    employee_id=emp.id,
                    month=datetime.now().month,
                    year=datetime.now().year,
                    basic_salary=emp.basic_salary,
                    allowances=500.0,
                    overtime_amount=200.0,
                    deductions=100.0,
                    net_salary=emp.basic_salary + 500.0 + 200.0 - 100.0,
                    status='approved',
                    created_by='admin'
                )
                db.session.add(payroll)

            # Sample leaves
            leave = Leave(
                employee_id=employees[0].id,
                leave_type='annual',
                start_date=date.today() + timedelta(days=7),
                end_date=date.today() + timedelta(days=10),
                days_count=4,
                reason='إجازة سنوية',
                status='pending'
            )
            db.session.add(leave)

            # Sample attendance
            for emp in employees:
                attendance = Attendance(
                    employee_id=emp.id,
                    date=date.today(),
                    check_in=datetime.strptime('08:00', '%H:%M').time(),
                    check_out=datetime.strptime('17:00', '%H:%M').time(),
                    working_hours=8.0,
                    overtime_hours=1.0,
                    status='present'
                )
                db.session.add(attendance)

            # Sample performance
            performance = Performance(
                employee_id=employees[0].id,
                evaluation_period='Q1 2024',
                evaluator='admin',
                quality_score=8.5,
                productivity_score=9.0,
                teamwork_score=8.0,
                communication_score=8.5,
                leadership_score=7.5,
                overall_score=8.3,
                comments='أداء ممتاز في جميع المجالات',
                goals='تطوير مهارات القيادة'
            )
            db.session.add(performance)

            # Sample documents
            for emp in employees:
                document = Document(
                    employee_id=emp.id,
                    title=f'عقد عمل - {emp.full_name}',
                    document_type='contract',
                    file_path=f'contract_{emp.employee_id}.pdf',
                    file_size=2048,
                    mime_type='application/pdf',
                    uploaded_by='admin'
                )
                db.session.add(document)

            db.session.commit()
            print("✅ تم إنشاء بيانات تجريبية شاملة")

    print("=" * 80)
    print("🎉 نظام إدارة شؤون الموظفين الكامل والمتقدم")
    print("=" * 80)
    print("✅ تم تشغيل النظام بنجاح مع جميع الميزات!")
    print("🌐 افتح المتصفح على: http://localhost:8080")
    print("🔑 اسم المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print("=" * 80)
    print("🚀 الميزات المتاحة والعاملة:")
    print("• ✅ إدارة الموظفين الكاملة (إضافة، عرض، تعديل)")
    print("• ✅ نظام الرواتب المتقدم (إنشاء كشوف، حساب تلقائي)")
    print("• ✅ إدارة الإجازات (طلب، اعتماد، رفض)")
    print("• ✅ نظام الحضور والانصراف (تسجيل، حساب ساعات)")
    print("• ✅ تقييم الأداء الشامل (معايير متعددة)")
    print("• ✅ إدارة المستندات (رفع، تصنيف)")
    print("• ✅ لوحة تحكم ذكية مع إحصائيات")
    print("• ✅ واجهة عربية احترافية")
    print("• ✅ بيانات تجريبية شاملة")
    print("=" * 80)
    print("📊 البيانات التجريبية تشمل:")
    print("• 3 موظفين مع بيانات كاملة")
    print("• كشوف رواتب للشهر الحالي")
    print("• طلب إجازة معلق")
    print("• سجلات حضور لليوم")
    print("• تقييم أداء نموذجي")
    print("• مستندات عقود العمل")
    print("=" * 80)
    print("اضغط Ctrl+C لإيقاف النظام")
    print("=" * 80)

    app.run(debug=True, host='0.0.0.0', port=8080)
