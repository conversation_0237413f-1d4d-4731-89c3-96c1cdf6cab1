#!/bin/bash

echo "========================================"
echo "    نظام إدارة شؤون الموظفين"
echo "========================================"
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "خطأ: Python 3 غير مثبت"
    echo "يرجى تثبيت Python 3.8 أو أحدث"
    exit 1
fi

echo "تحقق من وجود البيئة الافتراضية..."
if [ ! -d "venv" ]; then
    echo "إنشاء البيئة الافتراضية..."
    python3 -m venv venv
fi

echo "تفعيل البيئة الافتراضية..."
source venv/bin/activate

echo "تثبيت المتطلبات..."
pip install -r requirements.txt

if [ $? -ne 0 ]; then
    echo "خطأ في تثبيت المتطلبات"
    exit 1
fi

echo
echo "تشغيل التطبيق..."
echo "يمكنك الوصول للتطبيق على: http://localhost:5000"
echo "بيانات تسجيل الدخول الافتراضية:"
echo "اسم المستخدم: admin"
echo "كلمة المرور: admin123"
echo
echo "اضغط Ctrl+C لإيقاف التطبيق"
echo

python app.py
