{% extends "base.html" %}

{% block title %}{{ employee.full_name }} - عرض الموظف{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-person"></i>
        {{ employee.full_name_ar or employee.full_name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('employees.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-right"></i>
                العودة إلى القائمة
            </a>
            {% if current_user.role in ['admin', 'hr'] %}
            <a href="{{ url_for('employees.edit', id=employee.id) }}" class="btn btn-primary">
                <i class="bi bi-pencil"></i>
                تعديل
            </a>
            {% endif %}
        </div>
    </div>
</div>

<div class="row">
    <!-- Employee Profile -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-body text-center">
                {% if employee.profile_picture %}
                    <img src="{{ url_for('static', filename='uploads/' + employee.profile_picture) }}" 
                         alt="{{ employee.full_name }}" class="employee-profile-img mb-3">
                {% else %}
                    <div class="employee-profile-img bg-secondary d-flex align-items-center justify-content-center mb-3 mx-auto">
                        <i class="bi bi-person text-white" style="font-size: 4rem;"></i>
                    </div>
                {% endif %}
                
                <h4>{{ employee.full_name_ar or employee.full_name }}</h4>
                {% if employee.full_name_ar and employee.full_name_ar != employee.full_name %}
                    <p class="text-muted">{{ employee.full_name }}</p>
                {% endif %}
                
                <p class="text-muted mb-2">
                    <strong>{{ employee.position_ar or employee.position }}</strong>
                    {% if employee.position_ar and employee.position and employee.position_ar != employee.position %}
                        <br><small>{{ employee.position }}</small>
                    {% endif %}
                </p>
                
                <p class="text-muted">{{ employee.department }}</p>
                
                <div class="mb-3">
                    {% if employee.status == 'active' %}
                        <span class="badge bg-success fs-6">نشط</span>
                    {% elif employee.status == 'inactive' %}
                        <span class="badge bg-warning fs-6">غير نشط</span>
                    {% elif employee.status == 'terminated' %}
                        <span class="badge bg-danger fs-6">منتهي الخدمة</span>
                    {% endif %}
                </div>
                
                <div class="d-grid gap-2">
                    <a href="{{ url_for('documents.employee_documents', employee_id=employee.id) }}" 
                       class="btn btn-outline-primary">
                        <i class="bi bi-file-earmark-text"></i>
                        المستندات
                    </a>
                    <a href="{{ url_for('performance.employee_history', employee_id=employee.id) }}" 
                       class="btn btn-outline-info">
                        <i class="bi bi-graph-up"></i>
                        تاريخ التقييمات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Employee Details -->
    <div class="col-lg-8">
        <!-- Personal Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-person-lines-fill"></i>
                    المعلومات الشخصية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>رقم الموظف:</strong></td>
                                <td>{{ employee.employee_id }}</td>
                            </tr>
                            <tr>
                                <td><strong>رقم الهوية:</strong></td>
                                <td>{{ employee.national_id or '-' }}</td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الميلاد:</strong></td>
                                <td>{{ employee.birth_date.strftime('%Y-%m-%d') if employee.birth_date else '-' }}</td>
                            </tr>
                            <tr>
                                <td><strong>البريد الإلكتروني:</strong></td>
                                <td>
                                    {% if employee.email %}
                                        <a href="mailto:{{ employee.email }}">{{ employee.email }}</a>
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>رقم الهاتف:</strong></td>
                                <td>
                                    {% if employee.phone %}
                                        <a href="tel:{{ employee.phone }}">{{ employee.phone }}</a>
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>جهة الاتصال في الطوارئ:</strong></td>
                                <td>{{ employee.emergency_contact or '-' }}</td>
                            </tr>
                            <tr>
                                <td><strong>رقم هاتف الطوارئ:</strong></td>
                                <td>
                                    {% if employee.emergency_phone %}
                                        <a href="tel:{{ employee.emergency_phone }}">{{ employee.emergency_phone }}</a>
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>العنوان:</strong></td>
                                <td>{{ employee.address or '-' }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Job Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-briefcase"></i>
                    المعلومات الوظيفية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>تاريخ التوظيف:</strong></td>
                                <td>{{ employee.hire_date.strftime('%Y-%m-%d') }}</td>
                            </tr>
                            <tr>
                                <td><strong>سنوات الخدمة:</strong></td>
                                <td>
                                    {% set service_years = (moment().date() - employee.hire_date).days // 365 %}
                                    {{ ((moment().date() - employee.hire_date).days / 365.25) | round(1) }} سنة
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>الراتب الأساسي:</strong></td>
                                <td>{{ "{:,.2f}".format(employee.basic_salary) }} ر.س</td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ آخر تحديث:</strong></td>
                                <td>{{ employee.updated_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="row">
            <div class="col-md-3">
                <div class="card text-center stats-card">
                    <div class="card-body">
                        <h4>{{ employee.payrolls|length }}</h4>
                        <small>كشوف الرواتب</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center stats-card-2">
                    <div class="card-body">
                        <h4>{{ employee.leaves|length }}</h4>
                        <small>طلبات الإجازة</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center stats-card-3">
                    <div class="card-body">
                        <h4>{{ employee.attendances|length }}</h4>
                        <small>سجلات الحضور</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center stats-card-4">
                    <div class="card-body">
                        <h4>{{ employee.performances|length }}</h4>
                        <small>تقييمات الأداء</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-clock-history"></i>
                    الأنشطة الأخيرة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Recent Payrolls -->
                    <div class="col-md-6">
                        <h6>آخر كشوف الرواتب</h6>
                        {% if employee.payrolls %}
                            {% for payroll in employee.payrolls[:3] %}
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>{{ payroll.month }}/{{ payroll.year }}</span>
                                <span class="badge bg-{{ 'success' if payroll.status == 'paid' else 'warning' }}">
                                    {{ 'مدفوع' if payroll.status == 'paid' else 'معلق' }}
                                </span>
                            </div>
                            {% endfor %}
                            <a href="{{ url_for('payroll.index', employee_id=employee.id) }}" class="btn btn-sm btn-outline-primary">
                                عرض جميع الرواتب
                            </a>
                        {% else %}
                            <p class="text-muted">لا توجد كشوف رواتب</p>
                        {% endif %}
                    </div>

                    <!-- Recent Leaves -->
                    <div class="col-md-6">
                        <h6>آخر طلبات الإجازة</h6>
                        {% if employee.leaves %}
                            {% for leave in employee.leaves[:3] %}
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>{{ leave.leave_type }} - {{ leave.days_count }} أيام</span>
                                <span class="badge bg-{{ 'success' if leave.status == 'approved' else 'warning' if leave.status == 'pending' else 'danger' }}">
                                    {{ 'معتمد' if leave.status == 'approved' else 'معلق' if leave.status == 'pending' else 'مرفوض' }}
                                </span>
                            </div>
                            {% endfor %}
                            <a href="{{ url_for('leaves.index', employee_id=employee.id) }}" class="btn btn-sm btn-outline-primary">
                                عرض جميع الإجازات
                            </a>
                        {% else %}
                            <p class="text-muted">لا توجد طلبات إجازة</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
