{% extends "base.html" %}

{% block title %}إدارة الحضور والانصراف - نظام إدارة شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-clock"></i>
        إدارة الحضور والانصراف
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('attendance.add') }}" class="btn btn-primary">
                <i class="bi bi-clock"></i>
                تسجيل حضور
            </a>
            <a href="{{ url_for('attendance.daily_report') }}" class="btn btn-success">
                <i class="bi bi-calendar-day"></i>
                تقرير يومي
            </a>
        </div>
    </div>
</div>

<!-- Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="employee_id" class="form-label">الموظف</label>
                <select class="form-select" id="employee_id" name="employee_id">
                    <option value="">جميع الموظفين</option>
                    {% for emp in employees %}
                    <option value="{{ emp.id }}" {% if emp.id == employee_id %}selected{% endif %}>
                        {{ emp.employee_id }} - {{ emp.full_name_ar or emp.full_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="date" class="form-label">التاريخ</label>
                <input type="date" class="form-control" id="date" name="date" value="{{ date_filter }}">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">الحالة</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="present" {% if status == 'present' %}selected{% endif %}>حاضر</option>
                    <option value="late" {% if status == 'late' %}selected{% endif %}>متأخر</option>
                    <option value="absent" {% if status == 'absent' %}selected{% endif %}>غائب</option>
                    <option value="half_day" {% if status == 'half_day' %}selected{% endif %}>نصف يوم</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="bi bi-search"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Attendance Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">سجلات الحضور</h5>
        <span class="badge bg-primary">{{ attendances.total }} سجل</span>
    </div>
    <div class="card-body p-0">
        {% if attendances.items %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>الموظف</th>
                        <th>التاريخ</th>
                        <th>وقت الحضور</th>
                        <th>وقت الانصراف</th>
                        <th>ساعات العمل</th>
                        <th>العمل الإضافي</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for attendance in attendances.items %}
                    <tr>
                        <td>
                            <div>
                                <strong>{{ attendance.employee.full_name_ar or attendance.employee.full_name }}</strong>
                                <br>
                                <small class="text-muted">{{ attendance.employee.employee_id }}</small>
                            </div>
                        </td>
                        <td>{{ attendance.date.strftime('%Y-%m-%d') }}</td>
                        <td>
                            {% if attendance.check_in %}
                                {{ attendance.check_in.strftime('%H:%M') }}
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if attendance.check_out %}
                                {{ attendance.check_out.strftime('%H:%M') }}
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if attendance.working_hours %}
                                {{ "%.1f"|format(attendance.working_hours) }} ساعة
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if attendance.overtime_hours %}
                                {{ "%.1f"|format(attendance.overtime_hours) }} ساعة
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if attendance.status == 'present' %}
                                <span class="badge bg-success">حاضر</span>
                            {% elif attendance.status == 'late' %}
                                <span class="badge bg-warning">متأخر</span>
                            {% elif attendance.status == 'absent' %}
                                <span class="badge bg-danger">غائب</span>
                            {% elif attendance.status == 'half_day' %}
                                <span class="badge bg-info">نصف يوم</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('attendance.view', id=attendance.id) }}" 
                                   class="btn btn-outline-primary" title="عرض">
                                    <i class="bi bi-eye"></i>
                                </a>
                                {% if current_user.role in ['admin', 'hr'] %}
                                <a href="{{ url_for('attendance.edit', id=attendance.id) }}" 
                                   class="btn btn-outline-secondary" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-clock" style="font-size: 4rem; color: #dee2e6;"></i>
            <h5 class="mt-3 text-muted">لا توجد سجلات حضور</h5>
            <p class="text-muted">لم يتم العثور على أي سجلات حضور بناءً على معايير البحث المحددة.</p>
            <a href="{{ url_for('attendance.add') }}" class="btn btn-primary">
                <i class="bi bi-clock"></i>
                تسجيل حضور جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Pagination -->
{% if attendances.pages > 1 %}
<nav aria-label="تنقل الصفحات" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if attendances.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('attendance.index', page=attendances.prev_num, employee_id=employee_id, date=date_filter, status=status) }}">
                السابق
            </a>
        </li>
        {% endif %}
        
        {% for page_num in attendances.iter_pages() %}
            {% if page_num %}
                {% if page_num != attendances.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('attendance.index', page=page_num, employee_id=employee_id, date=date_filter, status=status) }}">
                        {{ page_num }}
                    </a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">…</span>
            </li>
            {% endif %}
        {% endfor %}
        
        {% if attendances.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('attendance.index', page=attendances.next_num, employee_id=employee_id, date=date_filter, status=status) }}">
                التالي
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}
{% endblock %}
