"""
ملف اختبار مبسط لنظام إدارة شؤون الموظفين
يمكن تشغيله بدون تثبيت جميع المتطلبات للاختبار السريع
"""

from flask import Flask, render_template_string

app = Flask(__name__)
app.secret_key = 'test-key'

# قالب HTML مبسط للاختبار
TEST_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة شؤون الموظفين - اختبار</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        .feature-card {
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="main-card p-5">
            <div class="text-center mb-5">
                <i class="bi bi-building text-primary" style="font-size: 4rem;"></i>
                <h1 class="mt-3">نظام إدارة شؤون الموظفين</h1>
                <p class="lead text-muted">نظام متكامل لإدارة الموارد البشرية</p>
                <div class="alert alert-success">
                    <i class="bi bi-check-circle"></i>
                    تم إنشاء النظام بنجاح! النظام جاهز للتشغيل.
                </div>
            </div>

            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card feature-card h-100 text-center">
                        <div class="card-body">
                            <i class="bi bi-people text-primary" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">إدارة الموظفين</h5>
                            <p class="text-muted">تسجيل وإدارة بيانات الموظفين مع دعم اللغتين العربية والإنجليزية</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card feature-card h-100 text-center">
                        <div class="card-body">
                            <i class="bi bi-cash-stack text-success" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">إدارة الرواتب</h5>
                            <p class="text-muted">حساب وإدارة الرواتب مع طباعة كشوف الرواتب بصيغة PDF</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card feature-card h-100 text-center">
                        <div class="card-body">
                            <i class="bi bi-calendar-x text-warning" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">إدارة الإجازات</h5>
                            <p class="text-muted">تقديم واعتماد طلبات الإجازات مع تتبع الأيام المستخدمة</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card feature-card h-100 text-center">
                        <div class="card-body">
                            <i class="bi bi-clock text-info" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">الحضور والانصراف</h5>
                            <p class="text-muted">تسجيل أوقات الحضور والانصراف مع حساب ساعات العمل</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card feature-card h-100 text-center">
                        <div class="card-body">
                            <i class="bi bi-graph-up text-purple" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">تقييم الأداء</h5>
                            <p class="text-muted">إنشاء تقييمات دورية للموظفين مع حفظ التاريخ</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card feature-card h-100 text-center">
                        <div class="card-body">
                            <i class="bi bi-file-earmark-text text-secondary" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">إدارة المستندات</h5>
                            <p class="text-muted">رفع وتنظيم مستندات الموظفين بأنواع ملفات متعددة</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-5">
                <h3 class="text-center mb-4">خطوات التشغيل</h3>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-windows"></i>
                                    Windows
                                </h5>
                            </div>
                            <div class="card-body">
                                <ol>
                                    <li>تأكد من تثبيت Python 3.8+</li>
                                    <li>انقر نقراً مزدوجاً على <code>run.bat</code></li>
                                    <li>انتظر تثبيت المتطلبات</li>
                                    <li>افتح المتصفح على <code>http://localhost:5000</code></li>
                                </ol>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-terminal"></i>
                                    Linux/Mac
                                </h5>
                            </div>
                            <div class="card-body">
                                <ol>
                                    <li>افتح Terminal</li>
                                    <li>نفذ الأمر: <code>chmod +x run.sh</code></li>
                                    <li>نفذ الأمر: <code>./run.sh</code></li>
                                    <li>افتح المتصفح على <code>http://localhost:5000</code></li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-4 text-center">
                <div class="alert alert-info">
                    <h5><i class="bi bi-info-circle"></i> بيانات تسجيل الدخول الافتراضية</h5>
                    <p class="mb-0">
                        <strong>اسم المستخدم:</strong> admin<br>
                        <strong>كلمة المرور:</strong> admin123
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
"""

@app.route('/')
def index():
    return render_template_string(TEST_TEMPLATE)

if __name__ == '__main__':
    print("=" * 50)
    print("    نظام إدارة شؤون الموظفين - اختبار")
    print("=" * 50)
    print("تم إنشاء النظام بنجاح!")
    print("افتح المتصفح على: http://localhost:5000")
    print("اضغط Ctrl+C لإيقاف الاختبار")
    print("=" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
