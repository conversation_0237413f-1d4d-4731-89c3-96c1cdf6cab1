{% extends "base.html" %}

{% block title %}إدارة الرواتب - نظام إدارة شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-cash-stack"></i>
        إدارة الرواتب
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('payroll.add') }}" class="btn btn-primary">
                <i class="bi bi-plus-lg"></i>
                إضافة راتب
            </a>
            <a href="{{ url_for('payroll.generate_monthly') }}" class="btn btn-success">
                <i class="bi bi-calendar-plus"></i>
                إنشاء رواتب شهرية
            </a>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stats-card text-center">
            <div class="card-body">
                <h4>{{ "{:,.0f}".format(total_gross) }}</h4>
                <small>إجمالي الاستحقاقات</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card-2 text-center">
            <div class="card-body">
                <h4>{{ "{:,.0f}".format(total_deductions) }}</h4>
                <small>إجمالي الخصومات</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card-3 text-center">
            <div class="card-body">
                <h4>{{ "{:,.0f}".format(total_net) }}</h4>
                <small>صافي الرواتب</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card-4 text-center">
            <div class="card-body">
                <h4>{{ payrolls.total }}</h4>
                <small>عدد كشوف الرواتب</small>
            </div>
        </div>
    </div>
</div>

<!-- Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-2">
                <label for="month" class="form-label">الشهر</label>
                <select class="form-select" id="month" name="month">
                    {% for i in range(1, 13) %}
                    <option value="{{ i }}" {% if i == month %}selected{% endif %}>
                        {{ i }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="year" class="form-label">السنة</label>
                <select class="form-select" id="year" name="year">
                    {% for y in range(2020, 2030) %}
                    <option value="{{ y }}" {% if y == year %}selected{% endif %}>
                        {{ y }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="employee_id" class="form-label">الموظف</label>
                <select class="form-select" id="employee_id" name="employee_id">
                    <option value="">جميع الموظفين</option>
                    {% for emp in employees %}
                    <option value="{{ emp.id }}" {% if emp.id == employee_id %}selected{% endif %}>
                        {{ emp.employee_id }} - {{ emp.full_name_ar or emp.full_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">الحالة</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="pending" {% if status == 'pending' %}selected{% endif %}>معلق</option>
                    <option value="paid" {% if status == 'paid' %}selected{% endif %}>مدفوع</option>
                    <option value="cancelled" {% if status == 'cancelled' %}selected{% endif %}>ملغي</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="bi bi-search"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Payrolls Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">كشوف الرواتب</h5>
        <span class="badge bg-primary">{{ payrolls.total }} كشف راتب</span>
    </div>
    <div class="card-body p-0">
        {% if payrolls.items %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>الموظف</th>
                        <th>الشهر/السنة</th>
                        <th>الراتب الأساسي</th>
                        <th>الاستحقاقات</th>
                        <th>الخصومات</th>
                        <th>صافي الراتب</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for payroll in payrolls.items %}
                    <tr>
                        <td>
                            <div>
                                <strong>{{ payroll.employee.full_name_ar or payroll.employee.full_name }}</strong>
                                <br>
                                <small class="text-muted">{{ payroll.employee.employee_id }}</small>
                            </div>
                        </td>
                        <td>{{ payroll.month }}/{{ payroll.year }}</td>
                        <td>{{ "{:,.2f}".format(payroll.basic_salary) }}</td>
                        <td>{{ "{:,.2f}".format(payroll.allowances + payroll.overtime + payroll.bonuses) }}</td>
                        <td>{{ "{:,.2f}".format(payroll.deductions + payroll.taxes + payroll.insurance) }}</td>
                        <td><strong>{{ "{:,.2f}".format(payroll.net_salary) }}</strong></td>
                        <td>
                            {% if payroll.status == 'paid' %}
                                <span class="badge bg-success">مدفوع</span>
                            {% elif payroll.status == 'pending' %}
                                <span class="badge bg-warning">معلق</span>
                            {% elif payroll.status == 'cancelled' %}
                                <span class="badge bg-danger">ملغي</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('payroll.view', id=payroll.id) }}" 
                                   class="btn btn-outline-primary" title="عرض">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{{ url_for('payroll.edit', id=payroll.id) }}" 
                                   class="btn btn-outline-secondary" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <a href="{{ url_for('payroll.print_pdf', id=payroll.id) }}" 
                                   class="btn btn-outline-info" title="طباعة PDF">
                                    <i class="bi bi-file-pdf"></i>
                                </a>
                                {% if current_user.role == 'admin' %}
                                <button type="button" class="btn btn-outline-danger" title="حذف"
                                        data-bs-toggle="modal" data-bs-target="#deleteModal{{ payroll.id }}">
                                    <i class="bi bi-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-cash-stack" style="font-size: 4rem; color: #dee2e6;"></i>
            <h5 class="mt-3 text-muted">لا توجد كشوف رواتب</h5>
            <p class="text-muted">لم يتم العثور على أي كشوف رواتب بناءً على معايير البحث المحددة.</p>
            <a href="{{ url_for('payroll.add') }}" class="btn btn-primary">
                <i class="bi bi-plus-lg"></i>
                إضافة كشف راتب جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Pagination -->
{% if payrolls.pages > 1 %}
<nav aria-label="تنقل الصفحات" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if payrolls.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('payroll.index', page=payrolls.prev_num, month=month, year=year, employee_id=employee_id, status=status) }}">
                السابق
            </a>
        </li>
        {% endif %}
        
        {% for page_num in payrolls.iter_pages() %}
            {% if page_num %}
                {% if page_num != payrolls.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('payroll.index', page=page_num, month=month, year=year, employee_id=employee_id, status=status) }}">
                        {{ page_num }}
                    </a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">…</span>
            </li>
            {% endif %}
        {% endfor %}
        
        {% if payrolls.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('payroll.index', page=payrolls.next_num, month=month, year=year, employee_id=employee_id, status=status) }}">
                التالي
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

<!-- Delete Modals -->
{% if current_user.role == 'admin' %}
{% for payroll in payrolls.items %}
<div class="modal fade" id="deleteModal{{ payroll.id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف كشف راتب الموظف <strong>{{ payroll.employee.full_name }}</strong> للشهر {{ payroll.month }}/{{ payroll.year }}؟</p>
                <p class="text-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    تحذير: لا يمكن التراجع عن هذا الإجراء.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="POST" action="{{ url_for('payroll.delete', id=payroll.id) }}" class="d-inline">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endfor %}
{% endif %}
{% endblock %}
