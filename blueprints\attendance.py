from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_required, current_user
from models import db, Employee, Attendance
from datetime import datetime, date, time

attendance_bp = Blueprint('attendance', __name__)

@attendance_bp.route('/')
@login_required
def index():
    page = request.args.get('page', 1, type=int)
    employee_id = request.args.get('employee_id', type=int)
    date_filter = request.args.get('date', '')
    status = request.args.get('status', '')
    
    query = Attendance.query
    
    if employee_id:
        query = query.filter_by(employee_id=employee_id)
    
    if date_filter:
        try:
            filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
            query = query.filter_by(date=filter_date)
        except ValueError:
            pass
    
    if status:
        query = query.filter_by(status=status)
    
    attendances = query.order_by(Attendance.date.desc()).paginate(
        page=page, per_page=15, error_out=False
    )
    
    employees = Employee.query.filter_by(status='active').all()
    
    return render_template('attendance/index.html',
                         attendances=attendances,
                         employees=employees,
                         employee_id=employee_id,
                         date_filter=date_filter,
                         status=status)

@attendance_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    if current_user.role not in ['admin', 'hr']:
        flash('ليس لديك صلاحية لإضافة سجلات الحضور', 'error')
        return redirect(url_for('attendance.index'))
    
    if request.method == 'POST':
        employee_id = request.form.get('employee_id', type=int)
        date_str = request.form.get('date')
        check_in_str = request.form.get('check_in')
        check_out_str = request.form.get('check_out')
        break_duration = request.form.get('break_duration', type=int) or 0
        overtime_hours = request.form.get('overtime_hours', type=float) or 0.0
        status = request.form.get('status')
        notes = request.form.get('notes')
        
        if not all([employee_id, date_str, status]):
            flash('الحقول المطلوبة: الموظف، التاريخ، الحالة', 'error')
            return render_template('attendance/add.html', employees=Employee.query.filter_by(status='active').all())
        
        try:
            attendance_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            check_in = None
            check_out = None
            
            if check_in_str:
                check_in = datetime.strptime(check_in_str, '%H:%M').time()
            if check_out_str:
                check_out = datetime.strptime(check_out_str, '%H:%M').time()
                
        except ValueError:
            flash('تنسيق التاريخ أو الوقت غير صحيح', 'error')
            return render_template('attendance/add.html', employees=Employee.query.filter_by(status='active').all())
        
        # Check if attendance already exists
        existing = Attendance.query.filter_by(
            employee_id=employee_id,
            date=attendance_date
        ).first()
        
        if existing:
            flash('سجل الحضور لهذا الموظف في هذا التاريخ موجود بالفعل', 'error')
            return render_template('attendance/add.html', employees=Employee.query.filter_by(status='active').all())
        
        attendance = Attendance(
            employee_id=employee_id,
            date=attendance_date,
            check_in=check_in,
            check_out=check_out,
            break_duration=break_duration,
            overtime_hours=overtime_hours,
            status=status,
            notes=notes
        )
        
        try:
            db.session.add(attendance)
            db.session.commit()
            flash(f'تم إضافة سجل الحضور للموظف {attendance.employee.full_name} بنجاح', 'success')
            return redirect(url_for('attendance.view', id=attendance.id))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إضافة سجل الحضور', 'error')
    
    employees = Employee.query.filter_by(status='active').all()
    return render_template('attendance/add.html', employees=employees)

@attendance_bp.route('/<int:id>')
@login_required
def view(id):
    attendance = Attendance.query.get_or_404(id)
    return render_template('attendance/view.html', attendance=attendance)

@attendance_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    if current_user.role not in ['admin', 'hr']:
        flash('ليس لديك صلاحية لتعديل سجلات الحضور', 'error')
        return redirect(url_for('attendance.view', id=id))
    
    attendance = Attendance.query.get_or_404(id)
    
    if request.method == 'POST':
        check_in_str = request.form.get('check_in')
        check_out_str = request.form.get('check_out')
        attendance.break_duration = request.form.get('break_duration', type=int) or 0
        attendance.overtime_hours = request.form.get('overtime_hours', type=float) or 0.0
        attendance.status = request.form.get('status')
        attendance.notes = request.form.get('notes')
        
        try:
            if check_in_str:
                attendance.check_in = datetime.strptime(check_in_str, '%H:%M').time()
            if check_out_str:
                attendance.check_out = datetime.strptime(check_out_str, '%H:%M').time()
        except ValueError:
            flash('تنسيق الوقت غير صحيح', 'error')
            return render_template('attendance/edit.html', attendance=attendance)
        
        try:
            db.session.commit()
            flash(f'تم تحديث سجل الحضور للموظف {attendance.employee.full_name} بنجاح', 'success')
            return redirect(url_for('attendance.view', id=attendance.id))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء تحديث سجل الحضور', 'error')
    
    return render_template('attendance/edit.html', attendance=attendance)

@attendance_bp.route('/daily_report')
@login_required
def daily_report():
    if current_user.role not in ['admin', 'hr']:
        flash('ليس لديك صلاحية لعرض التقارير', 'error')
        return redirect(url_for('attendance.index'))
    
    report_date = request.args.get('date', date.today().isoformat())
    
    try:
        report_date = datetime.strptime(report_date, '%Y-%m-%d').date()
    except ValueError:
        report_date = date.today()
    
    # Get all active employees
    employees = Employee.query.filter_by(status='active').all()
    
    # Get attendance records for the date
    attendances = Attendance.query.filter_by(date=report_date).all()
    attendance_dict = {att.employee_id: att for att in attendances}
    
    # Prepare report data
    report_data = []
    for employee in employees:
        attendance = attendance_dict.get(employee.id)
        report_data.append({
            'employee': employee,
            'attendance': attendance,
            'status': attendance.status if attendance else 'absent'
        })
    
    # Statistics
    total_employees = len(employees)
    present_count = len([att for att in attendances if att.status == 'present'])
    late_count = len([att for att in attendances if att.status == 'late'])
    absent_count = total_employees - len(attendances)
    
    return render_template('attendance/daily_report.html',
                         report_data=report_data,
                         report_date=report_date,
                         total_employees=total_employees,
                         present_count=present_count,
                         late_count=late_count,
                         absent_count=absent_count)

@attendance_bp.route('/monthly_summary')
@login_required
def monthly_summary():
    if current_user.role not in ['admin', 'hr']:
        flash('ليس لديك صلاحية لعرض التقارير', 'error')
        return redirect(url_for('attendance.index'))
    
    month = request.args.get('month', datetime.now().month, type=int)
    year = request.args.get('year', datetime.now().year, type=int)
    employee_id = request.args.get('employee_id', type=int)
    
    # Build query
    from sqlalchemy import extract
    query = Attendance.query.filter(
        extract('month', Attendance.date) == month,
        extract('year', Attendance.date) == year
    )
    
    if employee_id:
        query = query.filter_by(employee_id=employee_id)
    
    attendances = query.all()
    
    # Group by employee
    employee_summary = {}
    for att in attendances:
        emp_id = att.employee_id
        if emp_id not in employee_summary:
            employee_summary[emp_id] = {
                'employee': att.employee,
                'present_days': 0,
                'late_days': 0,
                'absent_days': 0,
                'total_hours': 0,
                'overtime_hours': 0
            }
        
        summary = employee_summary[emp_id]
        if att.status == 'present':
            summary['present_days'] += 1
        elif att.status == 'late':
            summary['late_days'] += 1
        
        summary['total_hours'] += att.working_hours
        summary['overtime_hours'] += att.overtime_hours
    
    employees = Employee.query.filter_by(status='active').all()
    
    return render_template('attendance/monthly_summary.html',
                         employee_summary=employee_summary,
                         employees=employees,
                         month=month,
                         year=year,
                         employee_id=employee_id)
