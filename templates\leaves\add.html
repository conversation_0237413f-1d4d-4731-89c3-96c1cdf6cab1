{% extends "base.html" %}

{% block title %}طلب إجازة جديد - نظام إدارة شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-calendar-plus"></i>
        طلب إجازة جديد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('leaves.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right"></i>
            العودة إلى قائمة الإجازات
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">بيانات طلب الإجازة</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="employee_id" class="form-label">الموظف <span class="text-danger">*</span></label>
                        <select class="form-select" id="employee_id" name="employee_id" required>
                            <option value="">اختر الموظف</option>
                            {% for employee in employees %}
                            <option value="{{ employee.id }}">
                                {{ employee.employee_id }} - {{ employee.full_name_ar or employee.full_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="leave_type" class="form-label">نوع الإجازة <span class="text-danger">*</span></label>
                        <select class="form-select" id="leave_type" name="leave_type" required>
                            <option value="">اختر نوع الإجازة</option>
                            <option value="annual">إجازة سنوية</option>
                            <option value="sick">إجازة مرضية</option>
                            <option value="emergency">إجازة طارئة</option>
                            <option value="maternity">إجازة أمومة</option>
                        </select>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="start_date" class="form-label">تاريخ البداية <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="start_date" name="start_date" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="end_date" class="form-label">تاريخ النهاية <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="end_date" name="end_date" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="reason" class="form-label">سبب الإجازة</label>
                        <textarea class="form-control" id="reason" name="reason" rows="4" placeholder="اكتب سبب طلب الإجازة..."></textarea>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-lg"></i>
                            تقديم طلب الإجازة
                        </button>
                        <a href="{{ url_for('leaves.index') }}" class="btn btn-secondary">
                            <i class="bi bi-x-lg"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle"></i>
                    معلومات مهمة
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="bi bi-lightbulb"></i> أنواع الإجازات:</h6>
                    <ul class="mb-0">
                        <li><strong>سنوية:</strong> الإجازة السنوية المستحقة</li>
                        <li><strong>مرضية:</strong> إجازة لأسباب صحية</li>
                        <li><strong>طارئة:</strong> إجازة لظروف طارئة</li>
                        <li><strong>أمومة:</strong> إجازة الوضع والأمومة</li>
                    </ul>
                </div>

                <div class="alert alert-warning">
                    <h6><i class="bi bi-exclamation-triangle"></i> تنبيه:</h6>
                    <p class="mb-0">سيتم إرسال طلب الإجازة للمراجعة والاعتماد من قبل إدارة الموارد البشرية.</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Calculate days automatically
document.addEventListener('DOMContentLoaded', function() {
    const startDate = document.getElementById('start_date');
    const endDate = document.getElementById('end_date');
    
    function calculateDays() {
        if (startDate.value && endDate.value) {
            const start = new Date(startDate.value);
            const end = new Date(endDate.value);
            const diffTime = Math.abs(end - start);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
            
            if (diffDays > 0) {
                // You can display the calculated days here if needed
                console.log('عدد الأيام:', diffDays);
            }
        }
    }
    
    startDate.addEventListener('change', calculateDays);
    endDate.addEventListener('change', calculateDays);
});
</script>
{% endblock %}
