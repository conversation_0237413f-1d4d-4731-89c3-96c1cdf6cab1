import os
from flask import Flask, render_template_string, request, session, redirect, url_for, flash, jsonify
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user, UserMixin
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date
import json

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'hr-system-secret-key-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///hr_complete_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
login_manager.login_message_category = 'info'

# Models
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(20), default='user')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Employee(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.String(20), unique=True, nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    first_name_ar = db.Column(db.String(50))
    last_name_ar = db.Column(db.String(50))
    email = db.Column(db.String(120), unique=True)
    phone = db.Column(db.String(20))
    department = db.Column(db.String(100))
    position = db.Column(db.String(100))
    basic_salary = db.Column(db.Float, default=0.0)
    hire_date = db.Column(db.Date, nullable=False, default=date.today)
    status = db.Column(db.String(20), default='active')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"
    
    @property
    def full_name_ar(self):
        if self.first_name_ar and self.last_name_ar:
            return f"{self.first_name_ar} {self.last_name_ar}"
        return self.full_name

class Payroll(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    month = db.Column(db.Integer, nullable=False)
    year = db.Column(db.Integer, nullable=False)
    basic_salary = db.Column(db.Float, default=0.0)
    allowances = db.Column(db.Float, default=0.0)
    overtime_amount = db.Column(db.Float, default=0.0)
    deductions = db.Column(db.Float, default=0.0)
    net_salary = db.Column(db.Float, default=0.0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    employee = db.relationship('Employee', backref='payrolls')

class Leave(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    leave_type = db.Column(db.String(50), nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    days_count = db.Column(db.Integer, nullable=False)
    reason = db.Column(db.Text)
    status = db.Column(db.String(20), default='pending')
    approved_by = db.Column(db.String(100))
    approved_date = db.Column(db.Date)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    employee = db.relationship('Employee', backref='leaves')

class Attendance(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    check_in = db.Column(db.Time)
    check_out = db.Column(db.Time)
    working_hours = db.Column(db.Float, default=0.0)
    overtime_hours = db.Column(db.Float, default=0.0)
    status = db.Column(db.String(20), default='present')
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    employee = db.relationship('Employee', backref='attendances')

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Routes
@app.route('/')
@login_required
def dashboard():
    total_employees = Employee.query.filter_by(status='active').count()
    recent_employees = Employee.query.order_by(Employee.created_at.desc()).limit(5).all()
    total_payrolls = Payroll.query.count()
    pending_leaves = Leave.query.filter_by(status='pending').count()
    
    return render_template_string(DASHBOARD_TEMPLATE, 
                                total_employees=total_employees,
                                recent_employees=recent_employees,
                                total_payrolls=total_payrolls,
                                pending_leaves=pending_leaves)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if not username or not password:
            flash('يرجى إدخال اسم المستخدم وكلمة المرور', 'error')
            return render_template_string(LOGIN_TEMPLATE)
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password) and user.is_active:
            login_user(user)
            flash(f'مرحباً {user.username}!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template_string(LOGIN_TEMPLATE)

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

@app.route('/employees')
@login_required
def employees():
    page = request.args.get('page', 1, type=int)
    employees = Employee.query.paginate(page=page, per_page=10, error_out=False)
    return render_template_string(EMPLOYEES_TEMPLATE, employees=employees)

@app.route('/employees/add', methods=['GET', 'POST'])
@login_required
def add_employee():
    if request.method == 'POST':
        employee_id = request.form.get('employee_id')
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        first_name_ar = request.form.get('first_name_ar')
        last_name_ar = request.form.get('last_name_ar')
        email = request.form.get('email')
        phone = request.form.get('phone')
        department = request.form.get('department')
        position = request.form.get('position')
        basic_salary = request.form.get('basic_salary', type=float) or 0.0
        hire_date_str = request.form.get('hire_date')
        
        if not all([employee_id, first_name, last_name, department, position]):
            flash('الحقول المطلوبة: رقم الموظف، الاسم الأول، الاسم الأخير، القسم، المنصب', 'error')
            return render_template_string(ADD_EMPLOYEE_TEMPLATE)
        
        # Check if employee ID already exists
        if Employee.query.filter_by(employee_id=employee_id).first():
            flash('رقم الموظف موجود بالفعل', 'error')
            return render_template_string(ADD_EMPLOYEE_TEMPLATE)
        
        try:
            hire_date = datetime.strptime(hire_date_str, '%Y-%m-%d').date() if hire_date_str else date.today()
        except ValueError:
            flash('تنسيق التاريخ غير صحيح', 'error')
            return render_template_string(ADD_EMPLOYEE_TEMPLATE)
        
        employee = Employee(
            employee_id=employee_id,
            first_name=first_name,
            last_name=last_name,
            first_name_ar=first_name_ar,
            last_name_ar=last_name_ar,
            email=email,
            phone=phone,
            department=department,
            position=position,
            basic_salary=basic_salary,
            hire_date=hire_date
        )
        
        try:
            db.session.add(employee)
            db.session.commit()
            flash(f'تم إضافة الموظف {employee.full_name} بنجاح', 'success')
            return redirect(url_for('employees'))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إضافة الموظف', 'error')
    
    return render_template_string(ADD_EMPLOYEE_TEMPLATE)

@app.route('/payroll')
@login_required
def payroll():
    page = request.args.get('page', 1, type=int)
    month = request.args.get('month', type=int)
    year = request.args.get('year', type=int)
    
    query = Payroll.query
    if month:
        query = query.filter_by(month=month)
    if year:
        query = query.filter_by(year=year)
    
    payrolls = query.order_by(Payroll.created_at.desc()).paginate(page=page, per_page=10, error_out=False)
    return render_template_string(PAYROLL_TEMPLATE, payrolls=payrolls, month=month, year=year)

@app.route('/leaves')
@login_required
def leaves():
    page = request.args.get('page', 1, type=int)
    status = request.args.get('status')
    
    query = Leave.query
    if status:
        query = query.filter_by(status=status)
    
    leaves = query.order_by(Leave.created_at.desc()).paginate(page=page, per_page=10, error_out=False)
    employees = Employee.query.filter_by(status='active').all()
    return render_template_string(LEAVES_TEMPLATE, leaves=leaves, employees=employees, status=status)

@app.route('/attendance')
@login_required
def attendance():
    page = request.args.get('page', 1, type=int)
    date_filter = request.args.get('date')
    
    query = Attendance.query
    if date_filter:
        try:
            filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
            query = query.filter_by(date=filter_date)
        except ValueError:
            pass
    
    attendances = query.order_by(Attendance.date.desc()).paginate(page=page, per_page=10, error_out=False)
    employees = Employee.query.filter_by(status='active').all()
    return render_template_string(ATTENDANCE_TEMPLATE, attendances=attendances, employees=employees, date_filter=date_filter)

# HTML Templates
LOGIN_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة شؤون الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Noto Sans Arabic', sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center; }
        .login-card { background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 1rem; box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1); }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card login-card">
                    <div class="card-header bg-primary text-white text-center py-4">
                        <i class="bi bi-building" style="font-size: 2rem;"></i>
                        <h4 class="mt-2">نظام إدارة شؤون الموظفين</h4>
                    </div>
                    <div class="card-body p-4">
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label class="form-label">اسم المستخدم</label>
                                <input type="text" class="form-control" name="username" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" name="password" required>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">تسجيل الدخول</button>
                            </div>
                        </form>
                        
                        <div class="text-center mt-3">
                            <small class="text-muted">admin / admin123</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
"""

ADD_EMPLOYEE_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة موظف - نظام إدارة شؤون الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Noto Sans Arabic', sans-serif; background-color: #f8f9fa; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="bi bi-building"></i> نظام إدارة شؤون الموظفين
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/logout">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-3">
                <div class="list-group">
                    <a href="/" class="list-group-item list-group-item-action">
                        <i class="bi bi-speedometer2"></i> لوحة التحكم
                    </a>
                    <a href="/employees" class="list-group-item list-group-item-action">
                        <i class="bi bi-people"></i> الموظفون
                    </a>
                    <a href="/employees/add" class="list-group-item list-group-item-action active">
                        <i class="bi bi-person-plus"></i> إضافة موظف
                    </a>
                    <a href="/payroll" class="list-group-item list-group-item-action">
                        <i class="bi bi-cash-stack"></i> الرواتب
                    </a>
                    <a href="/leaves" class="list-group-item list-group-item-action">
                        <i class="bi bi-calendar-x"></i> الإجازات
                    </a>
                    <a href="/attendance" class="list-group-item list-group-item-action">
                        <i class="bi bi-clock"></i> الحضور
                    </a>
                </div>
            </div>

            <div class="col-md-9">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <h1 class="h2 mb-4">
                    <i class="bi bi-person-plus"></i> إضافة موظف جديد
                </h1>

                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">بيانات الموظف</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">رقم الموظف *</label>
                                        <input type="text" class="form-control" name="employee_id" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" name="email">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">الاسم الأول (إنجليزي) *</label>
                                        <input type="text" class="form-control" name="first_name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">الاسم الأخير (إنجليزي) *</label>
                                        <input type="text" class="form-control" name="last_name" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">الاسم الأول (عربي)</label>
                                        <input type="text" class="form-control" name="first_name_ar">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">الاسم الأخير (عربي)</label>
                                        <input type="text" class="form-control" name="last_name_ar">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" name="phone">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">تاريخ التوظيف</label>
                                        <input type="date" class="form-control" name="hire_date">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">القسم *</label>
                                        <input type="text" class="form-control" name="department" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">المنصب *</label>
                                        <input type="text" class="form-control" name="position" required>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الراتب الأساسي</label>
                                <input type="number" class="form-control" name="basic_salary" step="0.01" min="0">
                            </div>

                            <div class="d-flex justify-content-between">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-lg"></i> حفظ الموظف
                                </button>
                                <a href="/employees" class="btn btn-secondary">
                                    <i class="bi bi-x-lg"></i> إلغاء
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
"""

PAYROLL_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الرواتب - نظام إدارة شؤون الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Noto Sans Arabic', sans-serif; background-color: #f8f9fa; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="bi bi-building"></i> نظام إدارة شؤون الموظفين
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/logout">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-3">
                <div class="list-group">
                    <a href="/" class="list-group-item list-group-item-action">
                        <i class="bi bi-speedometer2"></i> لوحة التحكم
                    </a>
                    <a href="/employees" class="list-group-item list-group-item-action">
                        <i class="bi bi-people"></i> الموظفون
                    </a>
                    <a href="/employees/add" class="list-group-item list-group-item-action">
                        <i class="bi bi-person-plus"></i> إضافة موظف
                    </a>
                    <a href="/payroll" class="list-group-item list-group-item-action active">
                        <i class="bi bi-cash-stack"></i> الرواتب
                    </a>
                    <a href="/leaves" class="list-group-item list-group-item-action">
                        <i class="bi bi-calendar-x"></i> الإجازات
                    </a>
                    <a href="/attendance" class="list-group-item list-group-item-action">
                        <i class="bi bi-clock"></i> الحضور
                    </a>
                </div>
            </div>

            <div class="col-md-9">
                <h1 class="h2 mb-4">
                    <i class="bi bi-cash-stack"></i> إدارة الرواتب
                </h1>

                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">كشوف الرواتب ({{ payrolls.total }})</h5>
                    </div>
                    <div class="card-body p-0">
                        {% if payrolls.items %}
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>الموظف</th>
                                        <th>الشهر/السنة</th>
                                        <th>الراتب الأساسي</th>
                                        <th>البدلات</th>
                                        <th>الخصومات</th>
                                        <th>صافي الراتب</th>
                                        <th>تاريخ الإنشاء</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for payroll in payrolls.items %}
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>{{ payroll.employee.full_name_ar or payroll.employee.full_name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ payroll.employee.employee_id }}</small>
                                            </div>
                                        </td>
                                        <td>{{ payroll.month }}/{{ payroll.year }}</td>
                                        <td>{{ "{:,.0f}".format(payroll.basic_salary) }} ريال</td>
                                        <td>{{ "{:,.0f}".format(payroll.allowances) }} ريال</td>
                                        <td>{{ "{:,.0f}".format(payroll.deductions) }} ريال</td>
                                        <td><strong>{{ "{:,.0f}".format(payroll.net_salary) }} ريال</strong></td>
                                        <td>{{ payroll.created_at.strftime('%Y-%m-%d') }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="bi bi-cash-stack" style="font-size: 4rem; color: #dee2e6;"></i>
                            <h5 class="mt-3 text-muted">لا توجد كشوف رواتب</h5>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
"""

LEAVES_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الإجازات - نظام إدارة شؤون الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Noto Sans Arabic', sans-serif; background-color: #f8f9fa; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="bi bi-building"></i> نظام إدارة شؤون الموظفين
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/logout">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-3">
                <div class="list-group">
                    <a href="/" class="list-group-item list-group-item-action">
                        <i class="bi bi-speedometer2"></i> لوحة التحكم
                    </a>
                    <a href="/employees" class="list-group-item list-group-item-action">
                        <i class="bi bi-people"></i> الموظفون
                    </a>
                    <a href="/employees/add" class="list-group-item list-group-item-action">
                        <i class="bi bi-person-plus"></i> إضافة موظف
                    </a>
                    <a href="/payroll" class="list-group-item list-group-item-action">
                        <i class="bi bi-cash-stack"></i> الرواتب
                    </a>
                    <a href="/leaves" class="list-group-item list-group-item-action active">
                        <i class="bi bi-calendar-x"></i> الإجازات
                    </a>
                    <a href="/attendance" class="list-group-item list-group-item-action">
                        <i class="bi bi-clock"></i> الحضور
                    </a>
                </div>
            </div>

            <div class="col-md-9">
                <h1 class="h2 mb-4">
                    <i class="bi bi-calendar-x"></i> إدارة الإجازات
                </h1>

                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">طلبات الإجازة ({{ leaves.total }})</h5>
                    </div>
                    <div class="card-body p-0">
                        {% if leaves.items %}
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>الموظف</th>
                                        <th>نوع الإجازة</th>
                                        <th>من تاريخ</th>
                                        <th>إلى تاريخ</th>
                                        <th>عدد الأيام</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الطلب</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for leave in leaves.items %}
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>{{ leave.employee.full_name_ar or leave.employee.full_name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ leave.employee.employee_id }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            {% if leave.leave_type == 'annual' %}
                                                <span class="badge bg-primary">سنوية</span>
                                            {% elif leave.leave_type == 'sick' %}
                                                <span class="badge bg-danger">مرضية</span>
                                            {% elif leave.leave_type == 'emergency' %}
                                                <span class="badge bg-warning">طارئة</span>
                                            {% elif leave.leave_type == 'maternity' %}
                                                <span class="badge bg-success">أمومة</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ leave.start_date.strftime('%Y-%m-%d') }}</td>
                                        <td>{{ leave.end_date.strftime('%Y-%m-%d') }}</td>
                                        <td><strong>{{ leave.days_count }}</strong></td>
                                        <td>
                                            {% if leave.status == 'approved' %}
                                                <span class="badge bg-success">معتمد</span>
                                            {% elif leave.status == 'pending' %}
                                                <span class="badge bg-warning">معلق</span>
                                            {% elif leave.status == 'rejected' %}
                                                <span class="badge bg-danger">مرفوض</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ leave.created_at.strftime('%Y-%m-%d') }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="bi bi-calendar-x" style="font-size: 4rem; color: #dee2e6;"></i>
                            <h5 class="mt-3 text-muted">لا توجد طلبات إجازة</h5>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
"""

ATTENDANCE_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الحضور - نظام إدارة شؤون الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Noto Sans Arabic', sans-serif; background-color: #f8f9fa; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="bi bi-building"></i> نظام إدارة شؤون الموظفين
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/logout">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-3">
                <div class="list-group">
                    <a href="/" class="list-group-item list-group-item-action">
                        <i class="bi bi-speedometer2"></i> لوحة التحكم
                    </a>
                    <a href="/employees" class="list-group-item list-group-item-action">
                        <i class="bi bi-people"></i> الموظفون
                    </a>
                    <a href="/employees/add" class="list-group-item list-group-item-action">
                        <i class="bi bi-person-plus"></i> إضافة موظف
                    </a>
                    <a href="/payroll" class="list-group-item list-group-item-action">
                        <i class="bi bi-cash-stack"></i> الرواتب
                    </a>
                    <a href="/leaves" class="list-group-item list-group-item-action">
                        <i class="bi bi-calendar-x"></i> الإجازات
                    </a>
                    <a href="/attendance" class="list-group-item list-group-item-action active">
                        <i class="bi bi-clock"></i> الحضور
                    </a>
                </div>
            </div>

            <div class="col-md-9">
                <h1 class="h2 mb-4">
                    <i class="bi bi-clock"></i> إدارة الحضور والانصراف
                </h1>

                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">سجلات الحضور ({{ attendances.total }})</h5>
                    </div>
                    <div class="card-body p-0">
                        {% if attendances.items %}
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>الموظف</th>
                                        <th>التاريخ</th>
                                        <th>وقت الحضور</th>
                                        <th>وقت الانصراف</th>
                                        <th>ساعات العمل</th>
                                        <th>العمل الإضافي</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for attendance in attendances.items %}
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>{{ attendance.employee.full_name_ar or attendance.employee.full_name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ attendance.employee.employee_id }}</small>
                                            </div>
                                        </td>
                                        <td>{{ attendance.date.strftime('%Y-%m-%d') }}</td>
                                        <td>
                                            {% if attendance.check_in %}
                                                {{ attendance.check_in.strftime('%H:%M') }}
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if attendance.check_out %}
                                                {{ attendance.check_out.strftime('%H:%M') }}
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if attendance.working_hours %}
                                                {{ "%.1f"|format(attendance.working_hours) }} ساعة
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if attendance.overtime_hours %}
                                                {{ "%.1f"|format(attendance.overtime_hours) }} ساعة
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if attendance.status == 'present' %}
                                                <span class="badge bg-success">حاضر</span>
                                            {% elif attendance.status == 'late' %}
                                                <span class="badge bg-warning">متأخر</span>
                                            {% elif attendance.status == 'absent' %}
                                                <span class="badge bg-danger">غائب</span>
                                            {% elif attendance.status == 'half_day' %}
                                                <span class="badge bg-info">نصف يوم</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="bi bi-clock" style="font-size: 4rem; color: #dee2e6;"></i>
                            <h5 class="mt-3 text-muted">لا توجد سجلات حضور</h5>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
"""

# Initialize database and create admin user
if __name__ == '__main__':
    with app.app_context():
        db.create_all()

        # Create default admin user if not exists
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                role='admin'
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
            db.session.commit()
            print("✅ تم إنشاء المستخدم الافتراضي: admin/admin123")

        # Create sample employees if none exist
        if Employee.query.count() == 0:
            sample_employees = [
                Employee(
                    employee_id='EMP001',
                    first_name='Ahmed',
                    last_name='Ali',
                    first_name_ar='أحمد',
                    last_name_ar='علي',
                    email='<EMAIL>',
                    phone='0501234567',
                    department='تقنية المعلومات',
                    position='مطور برمجيات',
                    basic_salary=8000.0,
                    hire_date=date(2024, 1, 15)
                ),
                Employee(
                    employee_id='EMP002',
                    first_name='Fatima',
                    last_name='Hassan',
                    first_name_ar='فاطمة',
                    last_name_ar='حسن',
                    email='<EMAIL>',
                    phone='0507654321',
                    department='الموارد البشرية',
                    position='أخصائي موارد بشرية',
                    basic_salary=6500.0,
                    hire_date=date(2024, 2, 1)
                ),
                Employee(
                    employee_id='EMP003',
                    first_name='Mohammed',
                    last_name='Salem',
                    first_name_ar='محمد',
                    last_name_ar='سالم',
                    email='<EMAIL>',
                    phone='0509876543',
                    department='المالية',
                    position='محاسب',
                    basic_salary=7000.0,
                    hire_date=date(2024, 1, 20)
                )
            ]

            for emp in sample_employees:
                db.session.add(emp)
            db.session.commit()
            print("✅ تم إنشاء موظفين تجريبيين")

    print("=" * 60)
    print("🎉 نظام إدارة شؤون الموظفين الكامل")
    print("=" * 60)
    print("✅ تم تشغيل النظام بنجاح!")
    print("🌐 افتح المتصفح على: http://localhost:5000")
    print("🔑 اسم المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print("=" * 60)
    print("الميزات المتاحة:")
    print("• إدارة الموظفين")
    print("• إدارة الرواتب")
    print("• إدارة الإجازات")
    print("• نظام الحضور والانصراف")
    print("• لوحة تحكم شاملة")
    print("=" * 60)
    print("اضغط Ctrl+C لإيقاف النظام")
    print("=" * 60)

    app.run(debug=True, host='0.0.0.0', port=5000)
