{% extends "base.html" %}

{% block title %}إدارة المستندات - نظام إدارة شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-file-earmark-text"></i>
        إدارة المستندات
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('documents.add') }}" class="btn btn-primary">
                <i class="bi bi-cloud-upload"></i>
                رفع مستند جديد
            </a>
        </div>
    </div>
</div>

<!-- Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="employee_id" class="form-label">الموظف</label>
                <select class="form-select" id="employee_id" name="employee_id">
                    <option value="">جميع الموظفين</option>
                    {% for emp in employees %}
                    <option value="{{ emp.id }}" {% if emp.id == employee_id %}selected{% endif %}>
                        {{ emp.employee_id }} - {{ emp.full_name_ar or emp.full_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <label for="document_type" class="form-label">نوع المستند</label>
                <select class="form-select" id="document_type" name="document_type">
                    <option value="">جميع الأنواع</option>
                    {% for doc_type in document_types %}
                    <option value="{{ doc_type }}" {% if doc_type == document_type %}selected{% endif %}>
                        {{ doc_type }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="bi bi-search"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Documents Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">المستندات</h5>
        <span class="badge bg-primary">{{ documents.total }} مستند</span>
    </div>
    <div class="card-body p-0">
        {% if documents.items %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>عنوان المستند</th>
                        <th>الموظف</th>
                        <th>نوع المستند</th>
                        <th>حجم الملف</th>
                        <th>تاريخ الرفع</th>
                        <th>رفع بواسطة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for document in documents.items %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="bi bi-{{ 'file-pdf' if document.mime_type == 'application/pdf' else 'file-image' if 'image' in (document.mime_type or '') else 'file-text' }} text-primary me-2"></i>
                                <div>
                                    <strong>{{ document.title }}</strong>
                                    {% if document.notes %}
                                        <br>
                                        <small class="text-muted">{{ document.notes[:50] }}{% if document.notes|length > 50 %}...{% endif %}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong>{{ document.employee.full_name_ar or document.employee.full_name }}</strong>
                                <br>
                                <small class="text-muted">{{ document.employee.employee_id }}</small>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ document.document_type }}</span>
                        </td>
                        <td>
                            {% if document.file_size %}
                                {% if document.file_size < 1024 %}
                                    {{ document.file_size }} بايت
                                {% elif document.file_size < 1048576 %}
                                    {{ "%.1f"|format(document.file_size / 1024) }} كيلوبايت
                                {% else %}
                                    {{ "%.1f"|format(document.file_size / 1048576) }} ميجابايت
                                {% endif %}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>{{ document.upload_date.strftime('%Y-%m-%d') }}</td>
                        <td>{{ document.uploaded_by or '-' }}</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('documents.view', id=document.id) }}" 
                                   class="btn btn-outline-primary" title="عرض">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{{ url_for('documents.download', id=document.id) }}" 
                                   class="btn btn-outline-success" title="تحميل">
                                    <i class="bi bi-download"></i>
                                </a>
                                {% if current_user.role in ['admin', 'hr'] %}
                                <a href="{{ url_for('documents.edit', id=document.id) }}" 
                                   class="btn btn-outline-secondary" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                {% endif %}
                                {% if current_user.role == 'admin' %}
                                <button type="button" class="btn btn-outline-danger" title="حذف"
                                        data-bs-toggle="modal" data-bs-target="#deleteModal{{ document.id }}">
                                    <i class="bi bi-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-file-earmark-text" style="font-size: 4rem; color: #dee2e6;"></i>
            <h5 class="mt-3 text-muted">لا توجد مستندات</h5>
            <p class="text-muted">لم يتم العثور على أي مستندات بناءً على معايير البحث المحددة.</p>
            <a href="{{ url_for('documents.add') }}" class="btn btn-primary">
                <i class="bi bi-cloud-upload"></i>
                رفع مستند جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Pagination -->
{% if documents.pages > 1 %}
<nav aria-label="تنقل الصفحات" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if documents.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('documents.index', page=documents.prev_num, employee_id=employee_id, document_type=document_type) }}">
                السابق
            </a>
        </li>
        {% endif %}
        
        {% for page_num in documents.iter_pages() %}
            {% if page_num %}
                {% if page_num != documents.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('documents.index', page=page_num, employee_id=employee_id, document_type=document_type) }}">
                        {{ page_num }}
                    </a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">…</span>
            </li>
            {% endif %}
        {% endfor %}
        
        {% if documents.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('documents.index', page=documents.next_num, employee_id=employee_id, document_type=document_type) }}">
                التالي
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

<!-- Delete Modals -->
{% if current_user.role == 'admin' %}
{% for document in documents.items %}
<div class="modal fade" id="deleteModal{{ document.id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف المستند <strong>{{ document.title }}</strong>؟</p>
                <p class="text-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    تحذير: سيتم حذف الملف نهائياً ولا يمكن استرداده.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="POST" action="{{ url_for('documents.delete', id=document.id) }}" class="d-inline">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endfor %}
{% endif %}

<!-- Document Statistics -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center stats-card">
            <div class="card-body">
                <h4>{{ documents.total }}</h4>
                <small>إجمالي المستندات</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center stats-card-2">
            <div class="card-body">
                <h4>{{ document_types|length }}</h4>
                <small>أنواع المستندات</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center stats-card-3">
            <div class="card-body">
                <h4>{{ employees|length }}</h4>
                <small>الموظفون</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center stats-card-4">
            <div class="card-body">
                <h4>
                    {% set total_size = documents.items|map(attribute='file_size')|select|sum %}
                    {% if total_size %}
                        {% if total_size < 1048576 %}
                            {{ "%.0f"|format(total_size / 1024) }} كيلو
                        {% else %}
                            {{ "%.1f"|format(total_size / 1048576) }} ميجا
                        {% endif %}
                    {% else %}
                        0
                    {% endif %}
                </h4>
                <small>إجمالي الحجم</small>
            </div>
        </div>
    </div>
</div>
{% endblock %}
