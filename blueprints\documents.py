from flask import Blueprint, render_template, request, redirect, url_for, flash, send_file, current_app
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from models import db, Employee, Document
import os

documents_bp = Blueprint('documents', __name__)

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

@documents_bp.route('/')
@login_required
def index():
    page = request.args.get('page', 1, type=int)
    employee_id = request.args.get('employee_id', type=int)
    document_type = request.args.get('document_type', '')
    
    query = Document.query
    
    if employee_id:
        query = query.filter_by(employee_id=employee_id)
    
    if document_type:
        query = query.filter_by(document_type=document_type)
    
    documents = query.order_by(Document.upload_date.desc()).paginate(
        page=page, per_page=15, error_out=False
    )
    
    employees = Employee.query.filter_by(status='active').all()
    
    # Get document types for filter
    document_types = db.session.query(Document.document_type).distinct().all()
    document_types = [d[0] for d in document_types if d[0]]
    
    return render_template('documents/index.html',
                         documents=documents,
                         employees=employees,
                         document_types=document_types,
                         employee_id=employee_id,
                         document_type=document_type)

@documents_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    if current_user.role not in ['admin', 'hr']:
        flash('ليس لديك صلاحية لإضافة المستندات', 'error')
        return redirect(url_for('documents.index'))
    
    if request.method == 'POST':
        employee_id = request.form.get('employee_id', type=int)
        title = request.form.get('title')
        document_type = request.form.get('document_type')
        notes = request.form.get('notes')
        
        if not all([employee_id, title, document_type]):
            flash('الحقول المطلوبة: الموظف، عنوان المستند، نوع المستند', 'error')
            return render_template('documents/add.html', employees=Employee.query.filter_by(status='active').all())
        
        # Handle file upload
        if 'document_file' not in request.files:
            flash('يرجى اختيار ملف للرفع', 'error')
            return render_template('documents/add.html', employees=Employee.query.filter_by(status='active').all())
        
        file = request.files['document_file']
        if file.filename == '':
            flash('لم يتم اختيار أي ملف', 'error')
            return render_template('documents/add.html', employees=Employee.query.filter_by(status='active').all())
        
        if not allowed_file(file.filename):
            flash('نوع الملف غير مدعوم', 'error')
            return render_template('documents/add.html', employees=Employee.query.filter_by(status='active').all())
        
        # Get employee for filename
        employee = Employee.query.get(employee_id)
        if not employee:
            flash('الموظف غير موجود', 'error')
            return render_template('documents/add.html', employees=Employee.query.filter_by(status='active').all())
        
        # Create secure filename
        filename = secure_filename(f"{employee.employee_id}_{document_type}_{file.filename}")
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'documents', filename)
        
        # Create documents directory if it doesn't exist
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        try:
            file.save(file_path)
            
            # Get file info
            file_size = os.path.getsize(file_path)
            mime_type = file.content_type
            
            # Create document record
            document = Document(
                employee_id=employee_id,
                title=title,
                document_type=document_type,
                file_path=f"documents/{filename}",
                file_size=file_size,
                mime_type=mime_type,
                uploaded_by=current_user.username,
                notes=notes
            )
            
            db.session.add(document)
            db.session.commit()
            
            flash(f'تم رفع المستند {title} للموظف {employee.full_name} بنجاح', 'success')
            return redirect(url_for('documents.view', id=document.id))
            
        except Exception as e:
            # Remove uploaded file if database operation fails
            if os.path.exists(file_path):
                os.remove(file_path)
            db.session.rollback()
            flash('حدث خطأ أثناء رفع المستند', 'error')
            print(f"Error uploading document: {e}")
    
    employees = Employee.query.filter_by(status='active').all()
    return render_template('documents/add.html', employees=employees)

@documents_bp.route('/<int:id>')
@login_required
def view(id):
    document = Document.query.get_or_404(id)
    return render_template('documents/view.html', document=document)

@documents_bp.route('/<int:id>/download')
@login_required
def download(id):
    document = Document.query.get_or_404(id)
    file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], document.file_path)
    
    if not os.path.exists(file_path):
        flash('الملف غير موجود', 'error')
        return redirect(url_for('documents.view', id=id))
    
    return send_file(file_path, as_attachment=True, download_name=document.title)

@documents_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    if current_user.role not in ['admin', 'hr']:
        flash('ليس لديك صلاحية لتعديل المستندات', 'error')
        return redirect(url_for('documents.view', id=id))
    
    document = Document.query.get_or_404(id)
    
    if request.method == 'POST':
        document.title = request.form.get('title')
        document.document_type = request.form.get('document_type')
        document.notes = request.form.get('notes')
        
        # Handle file replacement
        if 'document_file' in request.files:
            file = request.files['document_file']
            if file and file.filename and allowed_file(file.filename):
                # Remove old file
                old_file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], document.file_path)
                if os.path.exists(old_file_path):
                    os.remove(old_file_path)
                
                # Save new file
                filename = secure_filename(f"{document.employee.employee_id}_{document.document_type}_{file.filename}")
                file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'documents', filename)
                
                file.save(file_path)
                
                # Update document info
                document.file_path = f"documents/{filename}"
                document.file_size = os.path.getsize(file_path)
                document.mime_type = file.content_type
        
        try:
            db.session.commit()
            flash(f'تم تحديث المستند {document.title} بنجاح', 'success')
            return redirect(url_for('documents.view', id=document.id))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء تحديث المستند', 'error')
    
    return render_template('documents/edit.html', document=document)

@documents_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لحذف المستندات', 'error')
        return redirect(url_for('documents.view', id=id))
    
    document = Document.query.get_or_404(id)
    
    try:
        # Remove file from filesystem
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], document.file_path)
        if os.path.exists(file_path):
            os.remove(file_path)
        
        db.session.delete(document)
        db.session.commit()
        flash(f'تم حذف المستند {document.title} بنجاح', 'success')
        return redirect(url_for('documents.index'))
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء حذف المستند', 'error')
        return redirect(url_for('documents.view', id=id))

@documents_bp.route('/employee/<int:employee_id>')
@login_required
def employee_documents(employee_id):
    employee = Employee.query.get_or_404(employee_id)
    
    page = request.args.get('page', 1, type=int)
    document_type = request.args.get('document_type', '')
    
    query = Document.query.filter_by(employee_id=employee_id)
    
    if document_type:
        query = query.filter_by(document_type=document_type)
    
    documents = query.order_by(Document.upload_date.desc()).paginate(
        page=page, per_page=10, error_out=False
    )
    
    # Get document types for this employee
    document_types = db.session.query(Document.document_type)\
                              .filter_by(employee_id=employee_id)\
                              .distinct().all()
    document_types = [d[0] for d in document_types if d[0]]
    
    return render_template('documents/employee_documents.html',
                         employee=employee,
                         documents=documents,
                         document_types=document_types,
                         document_type=document_type)
