from flask import Blueprint, render_template, request, redirect, url_for, flash, make_response
from flask_login import login_required, current_user
from models import db, Employee, Payroll
from datetime import datetime, date
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import io
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side

payroll_bp = Blueprint('payroll', __name__)

@payroll_bp.route('/')
@login_required
def index():
    if current_user.role not in ['admin', 'hr']:
        flash('ليس لديك صلاحية لعرض الرواتب', 'error')
        return redirect(url_for('dashboard.index'))
    
    page = request.args.get('page', 1, type=int)
    month = request.args.get('month', datetime.now().month, type=int)
    year = request.args.get('year', datetime.now().year, type=int)
    employee_id = request.args.get('employee_id', type=int)
    status = request.args.get('status', '')
    
    query = Payroll.query
    
    if month and year:
        query = query.filter_by(month=month, year=year)
    
    if employee_id:
        query = query.filter_by(employee_id=employee_id)
    
    if status:
        query = query.filter_by(status=status)
    
    payrolls = query.order_by(Payroll.created_at.desc()).paginate(
        page=page, per_page=10, error_out=False
    )
    
    # Get all employees for filter
    employees = Employee.query.filter_by(status='active').all()
    
    # Calculate totals for current filter
    total_payrolls = query.all()
    total_gross = sum([p.basic_salary + p.allowances + p.overtime + p.bonuses for p in total_payrolls])
    total_deductions = sum([p.deductions + p.taxes + p.insurance for p in total_payrolls])
    total_net = sum([p.net_salary for p in total_payrolls])
    
    return render_template('payroll/index.html',
                         payrolls=payrolls,
                         employees=employees,
                         month=month,
                         year=year,
                         employee_id=employee_id,
                         status=status,
                         total_gross=total_gross,
                         total_deductions=total_deductions,
                         total_net=total_net)

@payroll_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    if current_user.role not in ['admin', 'hr']:
        flash('ليس لديك صلاحية لإضافة رواتب', 'error')
        return redirect(url_for('payroll.index'))
    
    if request.method == 'POST':
        employee_id = request.form.get('employee_id', type=int)
        month = request.form.get('month', type=int)
        year = request.form.get('year', type=int)
        basic_salary = request.form.get('basic_salary', type=float) or 0.0
        allowances = request.form.get('allowances', type=float) or 0.0
        overtime = request.form.get('overtime', type=float) or 0.0
        bonuses = request.form.get('bonuses', type=float) or 0.0
        deductions = request.form.get('deductions', type=float) or 0.0
        taxes = request.form.get('taxes', type=float) or 0.0
        insurance = request.form.get('insurance', type=float) or 0.0
        notes = request.form.get('notes')
        
        if not all([employee_id, month, year]):
            flash('الحقول المطلوبة: الموظف، الشهر، السنة', 'error')
            return render_template('payroll/add.html', employees=Employee.query.filter_by(status='active').all())
        
        # Check if payroll already exists for this employee and month
        existing_payroll = Payroll.query.filter_by(
            employee_id=employee_id,
            month=month,
            year=year
        ).first()
        
        if existing_payroll:
            flash('راتب هذا الموظف لهذا الشهر موجود بالفعل', 'error')
            return render_template('payroll/add.html', employees=Employee.query.filter_by(status='active').all())
        
        # Create new payroll
        payroll = Payroll(
            employee_id=employee_id,
            month=month,
            year=year,
            basic_salary=basic_salary,
            allowances=allowances,
            overtime=overtime,
            bonuses=bonuses,
            deductions=deductions,
            taxes=taxes,
            insurance=insurance,
            notes=notes
        )
        
        payroll.calculate_net_salary()
        
        try:
            db.session.add(payroll)
            db.session.commit()
            flash(f'تم إضافة راتب الموظف {payroll.employee.full_name} بنجاح', 'success')
            return redirect(url_for('payroll.view', id=payroll.id))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إضافة الراتب', 'error')
            print(f"Error adding payroll: {e}")
    
    employees = Employee.query.filter_by(status='active').all()
    return render_template('payroll/add.html', employees=employees)

@payroll_bp.route('/<int:id>')
@login_required
def view(id):
    if current_user.role not in ['admin', 'hr']:
        flash('ليس لديك صلاحية لعرض الرواتب', 'error')
        return redirect(url_for('dashboard.index'))
    
    payroll = Payroll.query.get_or_404(id)
    return render_template('payroll/view.html', payroll=payroll)

@payroll_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    if current_user.role not in ['admin', 'hr']:
        flash('ليس لديك صلاحية لتعديل الرواتب', 'error')
        return redirect(url_for('payroll.view', id=id))
    
    payroll = Payroll.query.get_or_404(id)
    
    if request.method == 'POST':
        payroll.basic_salary = request.form.get('basic_salary', type=float) or 0.0
        payroll.allowances = request.form.get('allowances', type=float) or 0.0
        payroll.overtime = request.form.get('overtime', type=float) or 0.0
        payroll.bonuses = request.form.get('bonuses', type=float) or 0.0
        payroll.deductions = request.form.get('deductions', type=float) or 0.0
        payroll.taxes = request.form.get('taxes', type=float) or 0.0
        payroll.insurance = request.form.get('insurance', type=float) or 0.0
        payroll.notes = request.form.get('notes')
        payroll.status = request.form.get('status')
        
        payment_date_str = request.form.get('payment_date')
        if payment_date_str:
            try:
                payroll.payment_date = datetime.strptime(payment_date_str, '%Y-%m-%d').date()
            except ValueError:
                flash('تنسيق تاريخ الدفع غير صحيح', 'error')
                return render_template('payroll/edit.html', payroll=payroll)
        
        payroll.calculate_net_salary()
        
        try:
            db.session.commit()
            flash(f'تم تحديث راتب الموظف {payroll.employee.full_name} بنجاح', 'success')
            return redirect(url_for('payroll.view', id=payroll.id))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء تحديث الراتب', 'error')
            print(f"Error updating payroll: {e}")
    
    return render_template('payroll/edit.html', payroll=payroll)

@payroll_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لحذف الرواتب', 'error')
        return redirect(url_for('payroll.view', id=id))

    payroll = Payroll.query.get_or_404(id)

    try:
        db.session.delete(payroll)
        db.session.commit()
        flash(f'تم حذف راتب الموظف {payroll.employee.full_name} بنجاح', 'success')
        return redirect(url_for('payroll.index'))
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء حذف الراتب', 'error')
        return redirect(url_for('payroll.view', id=id))

@payroll_bp.route('/generate_monthly', methods=['GET', 'POST'])
@login_required
def generate_monthly():
    if current_user.role not in ['admin', 'hr']:
        flash('ليس لديك صلاحية لإنشاء الرواتب الشهرية', 'error')
        return redirect(url_for('payroll.index'))

    if request.method == 'POST':
        month = request.form.get('month', type=int)
        year = request.form.get('year', type=int)
        department = request.form.get('department')

        if not month or not year:
            flash('يرجى تحديد الشهر والسنة', 'error')
            return render_template('payroll/generate_monthly.html')

        # Get employees
        query = Employee.query.filter_by(status='active')
        if department:
            query = query.filter_by(department=department)

        employees = query.all()
        created_count = 0

        for employee in employees:
            # Check if payroll already exists
            existing = Payroll.query.filter_by(
                employee_id=employee.id,
                month=month,
                year=year
            ).first()

            if not existing:
                payroll = Payroll(
                    employee_id=employee.id,
                    month=month,
                    year=year,
                    basic_salary=employee.basic_salary
                )
                payroll.calculate_net_salary()
                db.session.add(payroll)
                created_count += 1

        try:
            db.session.commit()
            flash(f'تم إنشاء {created_count} راتب للشهر {month}/{year}', 'success')
            return redirect(url_for('payroll.index', month=month, year=year))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إنشاء الرواتب', 'error')

    # Get departments for filter
    departments = db.session.query(Employee.department).distinct().all()
    departments = [d[0] for d in departments if d[0]]

    return render_template('payroll/generate_monthly.html', departments=departments)
