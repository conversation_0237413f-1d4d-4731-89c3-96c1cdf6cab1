import os
from flask import Flask, render_template, request, session, redirect, url_for
from flask_login import <PERSON><PERSON><PERSON>ana<PERSON>, current_user
from flask_babel import Babel, get_locale
from config import config
from models import db, User

def create_app(config_name=None):
    if config_name is None:
        config_name = os.environ.get('FLASK_CONFIG', 'default')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)
    
    # Initialize extensions
    db.init_app(app)
    
    # Initialize Flask-Login
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))
    
    # Initialize Babel for internationalization (optional)
    try:
        babel = Babel(app)

        @babel.localeselector
        def get_locale():
            # Check if language is set in session
            if 'language' in session:
                return session['language']
            # Check if language is in URL parameters
            if request.args.get('lang'):
                session['language'] = request.args.get('lang')
                return session['language']
            # Default to Arabic
            return 'ar'
    except:
        # If Babel is not available, use simple language handling
        def get_locale():
            return session.get('language', 'ar')
    
    # Create upload directory
    upload_dir = os.path.join(app.instance_path, 'uploads')
    os.makedirs(upload_dir, exist_ok=True)
    app.config['UPLOAD_FOLDER'] = upload_dir
    
    # Register Blueprints
    from blueprints.auth import auth_bp
    from blueprints.dashboard import dashboard_bp
    from blueprints.employees import employees_bp
    from blueprints.payroll import payroll_bp
    from blueprints.leaves import leaves_bp
    from blueprints.attendance import attendance_bp
    from blueprints.performance import performance_bp
    from blueprints.documents import documents_bp
    
    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(dashboard_bp, url_prefix='/')
    app.register_blueprint(employees_bp, url_prefix='/employees')
    app.register_blueprint(payroll_bp, url_prefix='/payroll')
    app.register_blueprint(leaves_bp, url_prefix='/leaves')
    app.register_blueprint(attendance_bp, url_prefix='/attendance')
    app.register_blueprint(performance_bp, url_prefix='/performance')
    app.register_blueprint(documents_bp, url_prefix='/documents')
    
    # Context processors
    @app.context_processor
    def inject_conf_vars():
        return {
            'LANGUAGES': app.config['LANGUAGES'],
            'CURRENT_LANGUAGE': get_locale()
        }
    
    # Language switching route
    @app.route('/set_language/<language>')
    def set_language(language=None):
        if language in app.config['LANGUAGES']:
            session['language'] = language
        return redirect(request.referrer or url_for('dashboard.index'))
    
    # Error handlers
    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return render_template('errors/500.html'), 500
    
    # Create database tables
    with app.app_context():
        db.create_all()
        
        # Create default admin user if not exists
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                role='admin'
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
            db.session.commit()
            print("Default admin user created: admin/admin123")
    
    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=5000)
