from flask import Blueprint, render_template, request, redirect, url_for, flash, session
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.urls import url_parse
from models import db, User

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        remember_me = request.form.get('remember_me')
        
        if not username or not password:
            flash('يرجى إدخال اسم المستخدم وكلمة المرور', 'error')
            return render_template('auth/login.html')
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password) and user.is_active:
            login_user(user, remember=bool(remember_me))
            next_page = request.args.get('next')
            if not next_page or url_parse(next_page).netloc != '':
                next_page = url_for('dashboard.index')
            flash(f'مرحباً {user.username}!', 'success')
            return redirect(next_page)
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('auth/login.html')

@auth_bp.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('auth.login'))

@auth_bp.route('/register', methods=['GET', 'POST'])
@login_required
def register():
    # Only admin can register new users
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لإنشاء مستخدمين جدد', 'error')
        return redirect(url_for('dashboard.index'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        role = request.form.get('role', 'user')
        
        if not username or not email or not password:
            flash('جميع الحقول مطلوبة', 'error')
            return render_template('auth/register.html')
        
        # Check if user already exists
        if User.query.filter_by(username=username).first():
            flash('اسم المستخدم موجود بالفعل', 'error')
            return render_template('auth/register.html')
        
        if User.query.filter_by(email=email).first():
            flash('البريد الإلكتروني موجود بالفعل', 'error')
            return render_template('auth/register.html')
        
        # Create new user
        user = User(
            username=username,
            email=email,
            role=role
        )
        user.set_password(password)
        
        try:
            db.session.add(user)
            db.session.commit()
            flash(f'تم إنشاء المستخدم {username} بنجاح', 'success')
            return redirect(url_for('auth.users'))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إنشاء المستخدم', 'error')
    
    return render_template('auth/register.html')

@auth_bp.route('/users')
@login_required
def users():
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لعرض المستخدمين', 'error')
        return redirect(url_for('dashboard.index'))
    
    page = request.args.get('page', 1, type=int)
    users = User.query.paginate(
        page=page, per_page=10, error_out=False
    )
    return render_template('auth/users.html', users=users)

@auth_bp.route('/users/<int:user_id>/toggle_status')
@login_required
def toggle_user_status(user_id):
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لتعديل المستخدمين', 'error')
        return redirect(url_for('dashboard.index'))
    
    user = User.query.get_or_404(user_id)
    if user.id == current_user.id:
        flash('لا يمكنك تعطيل حسابك الخاص', 'error')
        return redirect(url_for('auth.users'))
    
    user.is_active = not user.is_active
    try:
        db.session.commit()
        status = 'تم تفعيل' if user.is_active else 'تم تعطيل'
        flash(f'{status} المستخدم {user.username}', 'success')
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء تحديث المستخدم', 'error')
    
    return redirect(url_for('auth.users'))
