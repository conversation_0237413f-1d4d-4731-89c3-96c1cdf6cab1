{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام إدارة شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-speedometer2"></i>
        لوحة التحكم
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('dashboard.reports') }}" class="btn btn-outline-secondary">
                <i class="bi bi-bar-chart"></i>
                التقارير
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">إجمالي الموظفين</div>
                        <div class="h5 mb-0 font-weight-bold">{{ total_employees }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-people" style="font-size: 2rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card-2 h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">إجمالي الرواتب الشهرية</div>
                        <div class="h5 mb-0 font-weight-bold">{{ "{:,.0f}".format(total_payroll_amount) }} ر.س</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-cash-stack" style="font-size: 2rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card-3 h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">الحضور اليوم</div>
                        <div class="h5 mb-0 font-weight-bold">{{ present_today }}</div>
                        <div class="text-xs">غياب: {{ absent_today }} | تأخير: {{ late_today }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-clock" style="font-size: 2rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card-4 h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">طلبات الإجازات المعلقة</div>
                        <div class="h5 mb-0 font-weight-bold">{{ pending_leaves }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-calendar-x" style="font-size: 2rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Employees -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-person-plus"></i>
                    الموظفون الجدد
                </h5>
                <a href="{{ url_for('employees.index') }}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                {% if recent_employees %}
                    {% for employee in recent_employees %}
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            {% if employee.profile_picture %}
                                <img src="{{ url_for('static', filename='uploads/' + employee.profile_picture) }}" 
                                     alt="{{ employee.full_name }}" class="profile-img">
                            {% else %}
                                <div class="profile-img bg-secondary d-flex align-items-center justify-content-center">
                                    <i class="bi bi-person text-white"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">
                                <a href="{{ url_for('employees.view', id=employee.id) }}" class="text-decoration-none">
                                    {{ employee.full_name_ar or employee.full_name }}
                                </a>
                            </h6>
                            <small class="text-muted">{{ employee.position_ar or employee.position }} - {{ employee.department }}</small>
                            <br>
                            <small class="text-muted">{{ employee.hire_date.strftime('%Y-%m-%d') }}</small>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">لا توجد موظفون جدد</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Upcoming Birthdays -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-gift"></i>
                    أعياد الميلاد القادمة
                </h5>
            </div>
            <div class="card-body">
                {% if upcoming_birthdays %}
                    {% for employee in upcoming_birthdays %}
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            {% if employee.profile_picture %}
                                <img src="{{ url_for('static', filename='uploads/' + employee.profile_picture) }}" 
                                     alt="{{ employee.full_name }}" class="profile-img">
                            {% else %}
                                <div class="profile-img bg-warning d-flex align-items-center justify-content-center">
                                    <i class="bi bi-gift text-white"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">{{ employee.full_name_ar or employee.full_name }}</h6>
                            <small class="text-muted">{{ employee.department }}</small>
                            <br>
                            <small class="text-warning">
                                <i class="bi bi-calendar-event"></i>
                                {{ employee.birth_date.strftime('%m-%d') }}
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">لا توجد أعياد ميلاد قادمة</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Department Distribution -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-pie-chart"></i>
                    توزيع الموظفين حسب القسم
                </h5>
            </div>
            <div class="card-body">
                {% if department_stats %}
                    {% for dept, count in department_stats %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>{{ dept or 'غير محدد' }}</span>
                        <span class="badge bg-primary">{{ count }}</span>
                    </div>
                    <div class="progress mb-3" style="height: 8px;">
                        <div class="progress-bar" role="progressbar" 
                             style="width: {{ (count / total_employees * 100) if total_employees > 0 else 0 }}%"></div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">لا توجد بيانات</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-2">
                    <div class="col-6">
                        <a href="{{ url_for('employees.add') }}" class="btn btn-outline-primary w-100">
                            <i class="bi bi-person-plus"></i>
                            <br>
                            إضافة موظف
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="{{ url_for('payroll.add') }}" class="btn btn-outline-success w-100">
                            <i class="bi bi-cash-stack"></i>
                            <br>
                            إضافة راتب
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="{{ url_for('leaves.add') }}" class="btn btn-outline-warning w-100">
                            <i class="bi bi-calendar-plus"></i>
                            <br>
                            طلب إجازة
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="{{ url_for('attendance.add') }}" class="btn btn-outline-info w-100">
                            <i class="bi bi-clock"></i>
                            <br>
                            تسجيل حضور
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payroll Summary -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up"></i>
                    ملخص الرواتب الشهرية
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="border-end">
                            <h4 class="text-success">{{ paid_payrolls }}</h4>
                            <small class="text-muted">رواتب مدفوعة</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border-end">
                            <h4 class="text-warning">{{ pending_payrolls }}</h4>
                            <small class="text-muted">رواتب معلقة</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border-end">
                            <h4 class="text-info">{{ approved_leaves_this_month }}</h4>
                            <small class="text-muted">إجازات معتمدة هذا الشهر</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-primary">{{ total_departments }}</h4>
                        <small class="text-muted">إجمالي الأقسام</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
