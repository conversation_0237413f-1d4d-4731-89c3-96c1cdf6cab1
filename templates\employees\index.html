{% extends "base.html" %}

{% block title %}الموظفون - نظام إدارة شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-people"></i>
        إدارة الموظفين
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        {% if current_user.role in ['admin', 'hr'] %}
        <div class="btn-group me-2">
            <a href="{{ url_for('employees.add') }}" class="btn btn-primary">
                <i class="bi bi-person-plus"></i>
                إضافة موظف جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search }}" placeholder="البحث بالاسم أو رقم الموظف أو البريد الإلكتروني">
            </div>
            <div class="col-md-3">
                <label for="department" class="form-label">القسم</label>
                <select class="form-select" id="department" name="department">
                    <option value="">جميع الأقسام</option>
                    {% for dept in departments %}
                    <option value="{{ dept }}" {% if dept == department %}selected{% endif %}>
                        {{ dept }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">الحالة</label>
                <select class="form-select" id="status" name="status">
                    <option value="active" {% if status == 'active' %}selected{% endif %}>نشط</option>
                    <option value="inactive" {% if status == 'inactive' %}selected{% endif %}>غير نشط</option>
                    <option value="terminated" {% if status == 'terminated' %}selected{% endif %}>منتهي الخدمة</option>
                    <option value="" {% if not status %}selected{% endif %}>جميع الحالات</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="bi bi-search"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Employees Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">قائمة الموظفين</h5>
        <span class="badge bg-primary">{{ employees.total }} موظف</span>
    </div>
    <div class="card-body p-0">
        {% if employees.items %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>الصورة</th>
                        <th>رقم الموظف</th>
                        <th>الاسم</th>
                        <th>القسم</th>
                        <th>المنصب</th>
                        <th>تاريخ التوظيف</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in employees.items %}
                    <tr>
                        <td>
                            {% if employee.profile_picture %}
                                <img src="{{ url_for('static', filename='uploads/' + employee.profile_picture) }}" 
                                     alt="{{ employee.full_name }}" class="profile-img">
                            {% else %}
                                <div class="profile-img bg-secondary d-flex align-items-center justify-content-center">
                                    <i class="bi bi-person text-white"></i>
                                </div>
                            {% endif %}
                        </td>
                        <td>
                            <strong>{{ employee.employee_id }}</strong>
                        </td>
                        <td>
                            <div>
                                <strong>{{ employee.full_name_ar or employee.full_name }}</strong>
                                {% if employee.full_name_ar and employee.full_name_ar != employee.full_name %}
                                    <br>
                                    <small class="text-muted">{{ employee.full_name }}</small>
                                {% endif %}
                            </div>
                        </td>
                        <td>{{ employee.department or '-' }}</td>
                        <td>
                            {{ employee.position_ar or employee.position or '-' }}
                            {% if employee.position_ar and employee.position and employee.position_ar != employee.position %}
                                <br>
                                <small class="text-muted">{{ employee.position }}</small>
                            {% endif %}
                        </td>
                        <td>{{ employee.hire_date.strftime('%Y-%m-%d') }}</td>
                        <td>
                            {% if employee.status == 'active' %}
                                <span class="badge bg-success">نشط</span>
                            {% elif employee.status == 'inactive' %}
                                <span class="badge bg-warning">غير نشط</span>
                            {% elif employee.status == 'terminated' %}
                                <span class="badge bg-danger">منتهي الخدمة</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('employees.view', id=employee.id) }}" 
                                   class="btn btn-outline-primary" title="عرض">
                                    <i class="bi bi-eye"></i>
                                </a>
                                {% if current_user.role in ['admin', 'hr'] %}
                                <a href="{{ url_for('employees.edit', id=employee.id) }}" 
                                   class="btn btn-outline-secondary" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                {% endif %}
                                <a href="{{ url_for('documents.employee_documents', employee_id=employee.id) }}" 
                                   class="btn btn-outline-info" title="المستندات">
                                    <i class="bi bi-file-earmark-text"></i>
                                </a>
                                {% if current_user.role == 'admin' %}
                                <button type="button" class="btn btn-outline-danger" title="حذف"
                                        data-bs-toggle="modal" data-bs-target="#deleteModal{{ employee.id }}">
                                    <i class="bi bi-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-people" style="font-size: 4rem; color: #dee2e6;"></i>
            <h5 class="mt-3 text-muted">لا توجد موظفون</h5>
            <p class="text-muted">لم يتم العثور على أي موظفين بناءً على معايير البحث المحددة.</p>
            {% if current_user.role in ['admin', 'hr'] %}
            <a href="{{ url_for('employees.add') }}" class="btn btn-primary">
                <i class="bi bi-person-plus"></i>
                إضافة موظف جديد
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Pagination -->
{% if employees.pages > 1 %}
<nav aria-label="تنقل الصفحات" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if employees.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('employees.index', page=employees.prev_num, search=search, department=department, status=status) }}">
                السابق
            </a>
        </li>
        {% endif %}
        
        {% for page_num in employees.iter_pages() %}
            {% if page_num %}
                {% if page_num != employees.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('employees.index', page=page_num, search=search, department=department, status=status) }}">
                        {{ page_num }}
                    </a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">…</span>
            </li>
            {% endif %}
        {% endfor %}
        
        {% if employees.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('employees.index', page=employees.next_num, search=search, department=department, status=status) }}">
                التالي
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

<!-- Delete Modals -->
{% if current_user.role == 'admin' %}
{% for employee in employees.items %}
<div class="modal fade" id="deleteModal{{ employee.id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الموظف <strong>{{ employee.full_name }}</strong>؟</p>
                <p class="text-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    تحذير: سيتم حذف جميع البيانات المرتبطة بهذا الموظف (الرواتب، الإجازات، الحضور، التقييمات، المستندات).
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="POST" action="{{ url_for('employees.delete', id=employee.id) }}" class="d-inline">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endfor %}
{% endif %}
{% endblock %}
