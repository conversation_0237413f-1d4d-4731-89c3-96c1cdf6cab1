from flask import Blueprint, render_template, request
from flask_login import login_required, current_user
from models import db, Employee, Payroll, Leave, Attendance, Performance
from datetime import datetime, date, timedelta
from sqlalchemy import func, extract

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/')
@login_required
def index():
    # Get current month and year
    current_month = datetime.now().month
    current_year = datetime.now().year
    
    # Basic statistics
    total_employees = Employee.query.filter_by(status='active').count()
    total_departments = db.session.query(func.count(func.distinct(Employee.department))).scalar()
    
    # Payroll statistics for current month
    current_month_payroll = Payroll.query.filter_by(
        month=current_month, 
        year=current_year
    ).all()
    
    total_payroll_amount = sum([p.net_salary for p in current_month_payroll])
    paid_payrolls = len([p for p in current_month_payroll if p.status == 'paid'])
    pending_payrolls = len([p for p in current_month_payroll if p.status == 'pending'])
    
    # Leave statistics
    pending_leaves = Leave.query.filter_by(status='pending').count()
    from sqlalchemy import extract
    approved_leaves_this_month = Leave.query.filter(
        Leave.status == 'approved',
        extract('month', Leave.start_date) == current_month,
        extract('year', Leave.start_date) == current_year
    ).count()
    
    # Attendance statistics for today
    today = date.today()
    today_attendance = Attendance.query.filter_by(date=today).all()
    present_today = len([a for a in today_attendance if a.status == 'present'])
    absent_today = total_employees - len(today_attendance)
    late_today = len([a for a in today_attendance if a.status == 'late'])
    
    # Recent activities (last 10 employees added)
    recent_employees = Employee.query.order_by(Employee.created_at.desc()).limit(5).all()
    
    # Upcoming birthdays (next 30 days)
    today = date.today()
    next_month = today + timedelta(days=30)
    
    upcoming_birthdays = Employee.query.filter(
        Employee.status == 'active',
        Employee.birth_date.isnot(None)
    ).all()
    
    # Filter birthdays in the next 30 days
    upcoming_birthdays = [
        emp for emp in upcoming_birthdays 
        if emp.birth_date and is_birthday_upcoming(emp.birth_date, today, next_month)
    ][:5]
    
    # Department distribution
    department_stats = db.session.query(
        Employee.department,
        func.count(Employee.id).label('count')
    ).filter_by(status='active').group_by(Employee.department).all()
    
    # Monthly payroll trend (last 6 months)
    payroll_trend = []
    for i in range(6):
        month_date = datetime.now() - timedelta(days=30*i)
        month_payrolls = Payroll.query.filter_by(
            month=month_date.month,
            year=month_date.year
        ).all()
        total_amount = sum([p.net_salary for p in month_payrolls])
        payroll_trend.append({
            'month': month_date.strftime('%B %Y'),
            'amount': total_amount
        })
    
    payroll_trend.reverse()
    
    return render_template('dashboard/index.html',
                         total_employees=total_employees,
                         total_departments=total_departments,
                         total_payroll_amount=total_payroll_amount,
                         paid_payrolls=paid_payrolls,
                         pending_payrolls=pending_payrolls,
                         pending_leaves=pending_leaves,
                         approved_leaves_this_month=approved_leaves_this_month,
                         present_today=present_today,
                         absent_today=absent_today,
                         late_today=late_today,
                         recent_employees=recent_employees,
                         upcoming_birthdays=upcoming_birthdays,
                         department_stats=department_stats,
                         payroll_trend=payroll_trend)

def is_birthday_upcoming(birth_date, start_date, end_date):
    """Check if birthday falls within the given date range"""
    if not birth_date:
        return False
    
    # Get this year's birthday
    this_year_birthday = birth_date.replace(year=start_date.year)
    
    # If this year's birthday has passed, check next year's
    if this_year_birthday < start_date:
        this_year_birthday = birth_date.replace(year=start_date.year + 1)
    
    return start_date <= this_year_birthday <= end_date

@dashboard_bp.route('/reports')
@login_required
def reports():
    if current_user.role not in ['admin', 'hr']:
        flash('ليس لديك صلاحية لعرض التقارير', 'error')
        return redirect(url_for('dashboard.index'))
    
    # Get filter parameters
    report_type = request.args.get('type', 'employees')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    department = request.args.get('department')
    
    data = {}
    
    if report_type == 'employees':
        query = Employee.query
        if department:
            query = query.filter_by(department=department)
        data['employees'] = query.all()
        
    elif report_type == 'payroll':
        query = Payroll.query
        if start_date and end_date:
            start = datetime.strptime(start_date, '%Y-%m-%d').date()
            end = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(
                Payroll.created_at >= start,
                Payroll.created_at <= end
            )
        data['payrolls'] = query.all()
        
    elif report_type == 'attendance':
        query = Attendance.query
        if start_date and end_date:
            start = datetime.strptime(start_date, '%Y-%m-%d').date()
            end = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(
                Attendance.date >= start,
                Attendance.date <= end
            )
        data['attendances'] = query.all()
    
    # Get all departments for filter
    departments = db.session.query(Employee.department).distinct().all()
    departments = [d[0] for d in departments if d[0]]
    
    return render_template('dashboard/reports.html',
                         report_type=report_type,
                         data=data,
                         departments=departments,
                         start_date=start_date,
                         end_date=end_date,
                         department=department)
