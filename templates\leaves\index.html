{% extends "base.html" %}

{% block title %}إدارة الإجازات - نظام إدارة شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-calendar-x"></i>
        إدارة الإجازات
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('leaves.add') }}" class="btn btn-primary">
                <i class="bi bi-calendar-plus"></i>
                طلب إجازة جديد
            </a>
        </div>
    </div>
</div>

<!-- Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="employee_id" class="form-label">الموظف</label>
                <select class="form-select" id="employee_id" name="employee_id">
                    <option value="">جميع الموظفين</option>
                    {% for emp in employees %}
                    <option value="{{ emp.id }}" {% if emp.id == employee_id %}selected{% endif %}>
                        {{ emp.employee_id }} - {{ emp.full_name_ar or emp.full_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="leave_type" class="form-label">نوع الإجازة</label>
                <select class="form-select" id="leave_type" name="leave_type">
                    <option value="">جميع الأنواع</option>
                    <option value="annual" {% if leave_type == 'annual' %}selected{% endif %}>سنوية</option>
                    <option value="sick" {% if leave_type == 'sick' %}selected{% endif %}>مرضية</option>
                    <option value="emergency" {% if leave_type == 'emergency' %}selected{% endif %}>طارئة</option>
                    <option value="maternity" {% if leave_type == 'maternity' %}selected{% endif %}>أمومة</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">الحالة</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="pending" {% if status == 'pending' %}selected{% endif %}>معلق</option>
                    <option value="approved" {% if status == 'approved' %}selected{% endif %}>معتمد</option>
                    <option value="rejected" {% if status == 'rejected' %}selected{% endif %}>مرفوض</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="bi bi-search"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Leaves Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">طلبات الإجازة</h5>
        <span class="badge bg-primary">{{ leaves.total }} طلب</span>
    </div>
    <div class="card-body p-0">
        {% if leaves.items %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>الموظف</th>
                        <th>نوع الإجازة</th>
                        <th>من تاريخ</th>
                        <th>إلى تاريخ</th>
                        <th>عدد الأيام</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for leave in leaves.items %}
                    <tr>
                        <td>
                            <div>
                                <strong>{{ leave.employee.full_name_ar or leave.employee.full_name }}</strong>
                                <br>
                                <small class="text-muted">{{ leave.employee.employee_id }}</small>
                            </div>
                        </td>
                        <td>
                            {% if leave.leave_type == 'annual' %}
                                <span class="badge bg-primary">سنوية</span>
                            {% elif leave.leave_type == 'sick' %}
                                <span class="badge bg-danger">مرضية</span>
                            {% elif leave.leave_type == 'emergency' %}
                                <span class="badge bg-warning">طارئة</span>
                            {% elif leave.leave_type == 'maternity' %}
                                <span class="badge bg-success">أمومة</span>
                            {% endif %}
                        </td>
                        <td>{{ leave.start_date.strftime('%Y-%m-%d') }}</td>
                        <td>{{ leave.end_date.strftime('%Y-%m-%d') }}</td>
                        <td><strong>{{ leave.days_count }}</strong></td>
                        <td>
                            {% if leave.status == 'approved' %}
                                <span class="badge bg-success">معتمد</span>
                            {% elif leave.status == 'pending' %}
                                <span class="badge bg-warning">معلق</span>
                            {% elif leave.status == 'rejected' %}
                                <span class="badge bg-danger">مرفوض</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('leaves.view', id=leave.id) }}" 
                                   class="btn btn-outline-primary" title="عرض">
                                    <i class="bi bi-eye"></i>
                                </a>
                                {% if current_user.role in ['admin', 'hr'] and leave.status == 'pending' %}
                                <form method="POST" action="{{ url_for('leaves.approve', id=leave.id) }}" class="d-inline">
                                    <button type="submit" class="btn btn-outline-success" title="اعتماد">
                                        <i class="bi bi-check-lg"></i>
                                    </button>
                                </form>
                                <form method="POST" action="{{ url_for('leaves.reject', id=leave.id) }}" class="d-inline">
                                    <button type="submit" class="btn btn-outline-danger" title="رفض">
                                        <i class="bi bi-x-lg"></i>
                                    </button>
                                </form>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-calendar-x" style="font-size: 4rem; color: #dee2e6;"></i>
            <h5 class="mt-3 text-muted">لا توجد طلبات إجازة</h5>
            <p class="text-muted">لم يتم العثور على أي طلبات إجازة بناءً على معايير البحث المحددة.</p>
            <a href="{{ url_for('leaves.add') }}" class="btn btn-primary">
                <i class="bi bi-calendar-plus"></i>
                طلب إجازة جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Pagination -->
{% if leaves.pages > 1 %}
<nav aria-label="تنقل الصفحات" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if leaves.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('leaves.index', page=leaves.prev_num, employee_id=employee_id, leave_type=leave_type, status=status) }}">
                السابق
            </a>
        </li>
        {% endif %}
        
        {% for page_num in leaves.iter_pages() %}
            {% if page_num %}
                {% if page_num != leaves.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('leaves.index', page=page_num, employee_id=employee_id, leave_type=leave_type, status=status) }}">
                        {{ page_num }}
                    </a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">…</span>
            </li>
            {% endif %}
        {% endfor %}
        
        {% if leaves.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('leaves.index', page=leaves.next_num, employee_id=employee_id, leave_type=leave_type, status=status) }}">
                التالي
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}
{% endblock %}
