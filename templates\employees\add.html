{% extends "base.html" %}

{% block title %}إضافة موظف جديد - نظام إدارة شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-person-plus"></i>
        إضافة موظف جديد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('employees.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right"></i>
            العودة إلى قائمة الموظفين
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">بيانات الموظف</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="employee_id" class="form-label">رقم الموظف <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="employee_id" name="employee_id" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="national_id" class="form-label">رقم الهوية</label>
                                <input type="text" class="form-control" id="national_id" name="national_id">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="first_name" class="form-label">الاسم الأول (إنجليزي) <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="first_name" name="first_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="last_name" class="form-label">الاسم الأخير (إنجليزي) <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="last_name" name="last_name" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="first_name_ar" class="form-label">الاسم الأول (عربي)</label>
                                <input type="text" class="form-control" id="first_name_ar" name="first_name_ar">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="last_name_ar" class="form-label">الاسم الأخير (عربي)</label>
                                <input type="text" class="form-control" id="last_name_ar" name="last_name_ar">
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                            </div>
                        </div>
                    </div>

                    <!-- Dates -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="birth_date" class="form-label">تاريخ الميلاد</label>
                                <input type="date" class="form-control" id="birth_date" name="birth_date">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="hire_date" class="form-label">تاريخ التوظيف <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="hire_date" name="hire_date" required>
                            </div>
                        </div>
                    </div>

                    <!-- Job Information -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="department" class="form-label">القسم <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="department" name="department" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="position" class="form-label">المنصب (إنجليزي) <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="position" name="position" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="position_ar" class="form-label">المنصب (عربي)</label>
                                <input type="text" class="form-control" id="position_ar" name="position_ar">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="basic_salary" class="form-label">الراتب الأساسي</label>
                                <input type="number" class="form-control" id="basic_salary" name="basic_salary" step="0.01" min="0">
                            </div>
                        </div>
                    </div>

                    <!-- Address -->
                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                    </div>

                    <!-- Emergency Contact -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="emergency_contact" class="form-label">جهة الاتصال في الطوارئ</label>
                                <input type="text" class="form-control" id="emergency_contact" name="emergency_contact">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="emergency_phone" class="form-label">رقم هاتف الطوارئ</label>
                                <input type="tel" class="form-control" id="emergency_phone" name="emergency_phone">
                            </div>
                        </div>
                    </div>

                    <!-- Profile Picture -->
                    <div class="mb-3">
                        <label for="profile_picture" class="form-label">صورة الموظف</label>
                        <input type="file" class="form-control" id="profile_picture" name="profile_picture" accept="image/*">
                        <div class="form-text">الحد الأقصى لحجم الملف: 16 ميجابايت. الأنواع المدعومة: JPG, PNG, GIF</div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-lg"></i>
                            حفظ الموظف
                        </button>
                        <a href="{{ url_for('employees.index') }}" class="btn btn-secondary">
                            <i class="bi bi-x-lg"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle"></i>
                    معلومات مهمة
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="bi bi-lightbulb"></i> نصائح:</h6>
                    <ul class="mb-0">
                        <li>الحقول المميزة بـ <span class="text-danger">*</span> مطلوبة</li>
                        <li>رقم الموظف يجب أن يكون فريداً</li>
                        <li>يمكن إضافة الأسماء باللغتين العربية والإنجليزية</li>
                        <li>صورة الموظف اختيارية ويمكن إضافتها لاحقاً</li>
                    </ul>
                </div>

                <div class="alert alert-warning">
                    <h6><i class="bi bi-exclamation-triangle"></i> تنبيه:</h6>
                    <p class="mb-0">تأكد من صحة البيانات قبل الحفظ، خاصة رقم الموظف وتاريخ التوظيف.</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Set today's date as default for hire date
document.addEventListener('DOMContentLoaded', function() {
    const hireDateInput = document.getElementById('hire_date');
    if (!hireDateInput.value) {
        const today = new Date().toISOString().split('T')[0];
        hireDateInput.value = today;
    }
});
</script>
{% endblock %}
