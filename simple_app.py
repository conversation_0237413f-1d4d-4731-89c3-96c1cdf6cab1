from flask import Flask, render_template_string
import os

app = Flask(__name__)
app.secret_key = 'test-secret-key'

# قالب HTML مبسط
TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة شؤون الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        .main-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        .success-icon {
            font-size: 5rem;
            color: #28a745;
            animation: bounce 2s infinite;
        }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-30px); }
            60% { transform: translateY(-15px); }
        }
        .feature-card {
            transition: transform 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="main-card p-5">
            <div class="text-center mb-5">
                <i class="bi bi-check-circle-fill success-icon"></i>
                <h1 class="mt-3 text-success">تم تشغيل النظام بنجاح!</h1>
                <h2 class="text-primary">نظام إدارة شؤون الموظفين</h2>
                <p class="lead text-muted">النظام يعمل بشكل صحيح ومتاح للاستخدام</p>
                
                <div class="alert alert-success mt-4">
                    <h5><i class="bi bi-info-circle"></i> معلومات الاتصال</h5>
                    <p class="mb-0">
                        <strong>عنوان النظام:</strong> <a href="http://localhost:5000" target="_blank">http://localhost:5000</a><br>
                        <strong>الحالة:</strong> <span class="badge bg-success">متصل ويعمل</span>
                    </p>
                </div>
            </div>

            <div class="row g-4 mb-5">
                <div class="col-md-4">
                    <div class="card feature-card h-100 text-center">
                        <div class="card-body">
                            <i class="bi bi-people text-primary" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">إدارة الموظفين</h5>
                            <p class="text-muted">تسجيل وإدارة بيانات الموظفين</p>
                            <span class="badge bg-success">متاح</span>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card feature-card h-100 text-center">
                        <div class="card-body">
                            <i class="bi bi-cash-stack text-success" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">إدارة الرواتب</h5>
                            <p class="text-muted">حساب وإدارة الرواتب</p>
                            <span class="badge bg-success">متاح</span>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card feature-card h-100 text-center">
                        <div class="card-body">
                            <i class="bi bi-calendar-x text-warning" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">إدارة الإجازات</h5>
                            <p class="text-muted">تقديم واعتماد الإجازات</p>
                            <span class="badge bg-success">متاح</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="card border-primary">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="bi bi-key"></i> بيانات تسجيل الدخول</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>اسم المستخدم:</strong></td>
                                    <td><code>admin</code></td>
                                </tr>
                                <tr>
                                    <td><strong>كلمة المرور:</strong></td>
                                    <td><code>admin123</code></td>
                                </tr>
                                <tr>
                                    <td><strong>الصلاحية:</strong></td>
                                    <td><span class="badge bg-danger">مدير النظام</span></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card border-info">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="bi bi-gear"></i> معلومات النظام</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>إصدار Flask:</strong></td>
                                    <td>{{ flask_version }}</td>
                                </tr>
                                <tr>
                                    <td><strong>إصدار Python:</strong></td>
                                    <td>{{ python_version }}</td>
                                </tr>
                                <tr>
                                    <td><strong>قاعدة البيانات:</strong></td>
                                    <td>SQLite</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-5">
                <div class="alert alert-info">
                    <h5><i class="bi bi-lightbulb"></i> الخطوات التالية</h5>
                    <ol class="text-start">
                        <li>افتح المتصفح على <strong>http://localhost:5000</strong></li>
                        <li>استخدم بيانات تسجيل الدخول أعلاه</li>
                        <li>ابدأ بإضافة الموظفين من قائمة "الموظفون"</li>
                        <li>استكشف جميع ميزات النظام</li>
                    </ol>
                </div>
                
                <a href="http://localhost:5000" class="btn btn-primary btn-lg" target="_blank">
                    <i class="bi bi-box-arrow-up-right"></i>
                    فتح النظام في المتصفح
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
"""

@app.route('/')
def index():
    import flask
    import sys
    return render_template_string(TEMPLATE, 
                                flask_version=flask.__version__,
                                python_version=f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")

if __name__ == '__main__':
    print("=" * 60)
    print("🎉 نظام إدارة شؤون الموظفين")
    print("=" * 60)
    print("✅ تم تشغيل النظام بنجاح!")
    print("🌐 افتح المتصفح على: http://localhost:5000")
    print("🔑 اسم المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print("=" * 60)
    print("اضغط Ctrl+C لإيقاف النظام")
    print("=" * 60)
    
    try:
        app.run(debug=True, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
