{% extends "base.html" %}

{% block title %}عرض طلب الإجازة - {{ leave.employee.full_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-calendar-x"></i>
        طلب إجازة - {{ leave.employee.full_name_ar or leave.employee.full_name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('leaves.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-right"></i>
                العودة إلى القائمة
            </a>
            {% if current_user.role in ['admin', 'hr'] and leave.status == 'pending' %}
            <form method="POST" action="{{ url_for('leaves.approve', id=leave.id) }}" class="d-inline">
                <button type="submit" class="btn btn-success">
                    <i class="bi bi-check-lg"></i>
                    اعتماد
                </button>
            </form>
            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#rejectModal">
                <i class="bi bi-x-lg"></i>
                رفض
            </button>
            {% endif %}
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Leave Details -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle"></i>
                    تفاصيل طلب الإجازة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>الموظف:</strong></td>
                                <td>{{ leave.employee.full_name_ar or leave.employee.full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>رقم الموظف:</strong></td>
                                <td>{{ leave.employee.employee_id }}</td>
                            </tr>
                            <tr>
                                <td><strong>القسم:</strong></td>
                                <td>{{ leave.employee.department or '-' }}</td>
                            </tr>
                            <tr>
                                <td><strong>نوع الإجازة:</strong></td>
                                <td>
                                    {% if leave.leave_type == 'annual' %}
                                        <span class="badge bg-primary">سنوية</span>
                                    {% elif leave.leave_type == 'sick' %}
                                        <span class="badge bg-danger">مرضية</span>
                                    {% elif leave.leave_type == 'emergency' %}
                                        <span class="badge bg-warning">طارئة</span>
                                    {% elif leave.leave_type == 'maternity' %}
                                        <span class="badge bg-success">أمومة</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>تاريخ البداية:</strong></td>
                                <td>{{ leave.start_date.strftime('%Y-%m-%d') }}</td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ النهاية:</strong></td>
                                <td>{{ leave.end_date.strftime('%Y-%m-%d') }}</td>
                            </tr>
                            <tr>
                                <td><strong>عدد الأيام:</strong></td>
                                <td><strong class="text-primary">{{ leave.days_count }} يوم</strong></td>
                            </tr>
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    {% if leave.status == 'approved' %}
                                        <span class="badge bg-success fs-6">معتمد</span>
                                    {% elif leave.status == 'pending' %}
                                        <span class="badge bg-warning fs-6">معلق</span>
                                    {% elif leave.status == 'rejected' %}
                                        <span class="badge bg-danger fs-6">مرفوض</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                {% if leave.reason %}
                <div class="mt-3">
                    <h6>سبب الإجازة:</h6>
                    <p class="text-muted">{{ leave.reason }}</p>
                </div>
                {% endif %}

                {% if leave.notes %}
                <div class="mt-3">
                    <h6>ملاحظات:</h6>
                    <p class="text-muted">{{ leave.notes }}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Approval Information -->
        {% if leave.approved_by %}
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-person-check"></i>
                    معلومات الاعتماد
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>معتمد بواسطة:</strong></td>
                                <td>{{ leave.approved_by }}</td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الاعتماد:</strong></td>
                                <td>{{ leave.approved_date.strftime('%Y-%m-%d') if leave.approved_date else '-' }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <div class="col-lg-4">
        <!-- Timeline -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-clock-history"></i>
                    تاريخ الطلب
                </h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">تم تقديم الطلب</h6>
                            <p class="timeline-text">{{ leave.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                        </div>
                    </div>
                    
                    {% if leave.approved_by %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-{{ 'success' if leave.status == 'approved' else 'danger' }}"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">
                                {% if leave.status == 'approved' %}
                                    تم اعتماد الطلب
                                {% else %}
                                    تم رفض الطلب
                                {% endif %}
                            </h6>
                            <p class="timeline-text">
                                {{ leave.approved_date.strftime('%Y-%m-%d') if leave.approved_date else '-' }}
                                <br>
                                <small class="text-muted">بواسطة: {{ leave.approved_by }}</small>
                            </p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reject Modal -->
{% if current_user.role in ['admin', 'hr'] and leave.status == 'pending' %}
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">رفض طلب الإجازة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('leaves.reject', id=leave.id) }}">
                <div class="modal-body">
                    <p>هل أنت متأكد من رفض طلب الإجازة للموظف <strong>{{ leave.employee.full_name }}</strong>؟</p>
                    <div class="mb-3">
                        <label for="rejection_reason" class="form-label">سبب الرفض:</label>
                        <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="3" placeholder="اكتب سبب رفض الطلب..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">رفض الطلب</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 17px;
    width: 2px;
    height: calc(100% + 8px);
    background-color: #dee2e6;
}

.timeline-title {
    margin-bottom: 5px;
    font-size: 14px;
    font-weight: 600;
}

.timeline-text {
    margin-bottom: 0;
    font-size: 13px;
    color: #6c757d;
}
</style>
{% endblock %}
