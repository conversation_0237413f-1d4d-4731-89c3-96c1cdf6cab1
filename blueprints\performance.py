from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_required, current_user
from models import db, Employee, Performance
from datetime import datetime, date

performance_bp = Blueprint('performance', __name__)

@performance_bp.route('/')
@login_required
def index():
    if current_user.role not in ['admin', 'hr']:
        flash('ليس لديك صلاحية لعرض تقييمات الأداء', 'error')
        return redirect(url_for('dashboard.index'))
    
    page = request.args.get('page', 1, type=int)
    employee_id = request.args.get('employee_id', type=int)
    period = request.args.get('period', '')
    
    query = Performance.query
    
    if employee_id:
        query = query.filter_by(employee_id=employee_id)
    
    if period:
        query = query.filter(Performance.evaluation_period.contains(period))
    
    performances = query.order_by(Performance.evaluation_date.desc()).paginate(
        page=page, per_page=10, error_out=False
    )
    
    employees = Employee.query.filter_by(status='active').all()
    
    return render_template('performance/index.html',
                         performances=performances,
                         employees=employees,
                         employee_id=employee_id,
                         period=period)

@performance_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    if current_user.role not in ['admin', 'hr']:
        flash('ليس لديك صلاحية لإضافة تقييمات الأداء', 'error')
        return redirect(url_for('performance.index'))
    
    if request.method == 'POST':
        employee_id = request.form.get('employee_id', type=int)
        evaluation_period = request.form.get('evaluation_period')
        evaluator = request.form.get('evaluator')
        quality_score = request.form.get('quality_score', type=int)
        productivity_score = request.form.get('productivity_score', type=int)
        teamwork_score = request.form.get('teamwork_score', type=int)
        communication_score = request.form.get('communication_score', type=int)
        leadership_score = request.form.get('leadership_score', type=int)
        strengths = request.form.get('strengths')
        areas_for_improvement = request.form.get('areas_for_improvement')
        goals = request.form.get('goals')
        comments = request.form.get('comments')
        evaluation_date_str = request.form.get('evaluation_date')
        
        if not all([employee_id, evaluation_period, evaluator]):
            flash('الحقول المطلوبة: الموظف، فترة التقييم، المقيم', 'error')
            return render_template('performance/add.html', employees=Employee.query.filter_by(status='active').all())
        
        try:
            evaluation_date = datetime.strptime(evaluation_date_str, '%Y-%m-%d').date() if evaluation_date_str else date.today()
        except ValueError:
            flash('تنسيق التاريخ غير صحيح', 'error')
            return render_template('performance/add.html', employees=Employee.query.filter_by(status='active').all())
        
        # Check if performance evaluation already exists for this employee and period
        existing = Performance.query.filter_by(
            employee_id=employee_id,
            evaluation_period=evaluation_period
        ).first()
        
        if existing:
            flash('تقييم الأداء لهذا الموظف في هذه الفترة موجود بالفعل', 'error')
            return render_template('performance/add.html', employees=Employee.query.filter_by(status='active').all())
        
        performance = Performance(
            employee_id=employee_id,
            evaluation_period=evaluation_period,
            evaluator=evaluator,
            quality_score=quality_score,
            productivity_score=productivity_score,
            teamwork_score=teamwork_score,
            communication_score=communication_score,
            leadership_score=leadership_score,
            strengths=strengths,
            areas_for_improvement=areas_for_improvement,
            goals=goals,
            comments=comments,
            evaluation_date=evaluation_date
        )
        
        performance.calculate_overall_score()
        
        try:
            db.session.add(performance)
            db.session.commit()
            flash(f'تم إضافة تقييم الأداء للموظف {performance.employee.full_name} بنجاح', 'success')
            return redirect(url_for('performance.view', id=performance.id))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إضافة تقييم الأداء', 'error')
    
    employees = Employee.query.filter_by(status='active').all()
    return render_template('performance/add.html', employees=employees)

@performance_bp.route('/<int:id>')
@login_required
def view(id):
    if current_user.role not in ['admin', 'hr']:
        flash('ليس لديك صلاحية لعرض تقييمات الأداء', 'error')
        return redirect(url_for('dashboard.index'))
    
    performance = Performance.query.get_or_404(id)
    
    # Get previous evaluations for this employee
    previous_evaluations = Performance.query.filter(
        Performance.employee_id == performance.employee_id,
        Performance.id != performance.id
    ).order_by(Performance.evaluation_date.desc()).limit(5).all()
    
    return render_template('performance/view.html', 
                         performance=performance,
                         previous_evaluations=previous_evaluations)

@performance_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    if current_user.role not in ['admin', 'hr']:
        flash('ليس لديك صلاحية لتعديل تقييمات الأداء', 'error')
        return redirect(url_for('performance.view', id=id))
    
    performance = Performance.query.get_or_404(id)
    
    if request.method == 'POST':
        performance.evaluation_period = request.form.get('evaluation_period')
        performance.evaluator = request.form.get('evaluator')
        performance.quality_score = request.form.get('quality_score', type=int)
        performance.productivity_score = request.form.get('productivity_score', type=int)
        performance.teamwork_score = request.form.get('teamwork_score', type=int)
        performance.communication_score = request.form.get('communication_score', type=int)
        performance.leadership_score = request.form.get('leadership_score', type=int)
        performance.strengths = request.form.get('strengths')
        performance.areas_for_improvement = request.form.get('areas_for_improvement')
        performance.goals = request.form.get('goals')
        performance.comments = request.form.get('comments')
        
        evaluation_date_str = request.form.get('evaluation_date')
        if evaluation_date_str:
            try:
                performance.evaluation_date = datetime.strptime(evaluation_date_str, '%Y-%m-%d').date()
            except ValueError:
                flash('تنسيق التاريخ غير صحيح', 'error')
                return render_template('performance/edit.html', performance=performance)
        
        performance.calculate_overall_score()
        
        try:
            db.session.commit()
            flash(f'تم تحديث تقييم الأداء للموظف {performance.employee.full_name} بنجاح', 'success')
            return redirect(url_for('performance.view', id=performance.id))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء تحديث تقييم الأداء', 'error')
    
    return render_template('performance/edit.html', performance=performance)

@performance_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لحذف تقييمات الأداء', 'error')
        return redirect(url_for('performance.view', id=id))
    
    performance = Performance.query.get_or_404(id)
    
    try:
        db.session.delete(performance)
        db.session.commit()
        flash(f'تم حذف تقييم الأداء للموظف {performance.employee.full_name} بنجاح', 'success')
        return redirect(url_for('performance.index'))
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء حذف تقييم الأداء', 'error')
        return redirect(url_for('performance.view', id=id))

@performance_bp.route('/employee/<int:employee_id>/history')
@login_required
def employee_history(employee_id):
    if current_user.role not in ['admin', 'hr']:
        flash('ليس لديك صلاحية لعرض تاريخ التقييمات', 'error')
        return redirect(url_for('dashboard.index'))
    
    employee = Employee.query.get_or_404(employee_id)
    
    performances = Performance.query.filter_by(employee_id=employee_id)\
                                  .order_by(Performance.evaluation_date.desc()).all()
    
    # Calculate performance trends
    scores_trend = []
    for perf in reversed(performances):
        if perf.overall_score:
            scores_trend.append({
                'period': perf.evaluation_period,
                'score': perf.overall_score,
                'date': perf.evaluation_date
            })
    
    return render_template('performance/employee_history.html',
                         employee=employee,
                         performances=performances,
                         scores_trend=scores_trend)

@performance_bp.route('/reports')
@login_required
def reports():
    if current_user.role not in ['admin', 'hr']:
        flash('ليس لديك صلاحية لعرض تقارير الأداء', 'error')
        return redirect(url_for('dashboard.index'))
    
    # Get performance statistics
    total_evaluations = Performance.query.count()
    
    # Average scores
    avg_quality = db.session.query(db.func.avg(Performance.quality_score)).scalar() or 0
    avg_productivity = db.session.query(db.func.avg(Performance.productivity_score)).scalar() or 0
    avg_teamwork = db.session.query(db.func.avg(Performance.teamwork_score)).scalar() or 0
    avg_communication = db.session.query(db.func.avg(Performance.communication_score)).scalar() or 0
    avg_leadership = db.session.query(db.func.avg(Performance.leadership_score)).scalar() or 0
    avg_overall = db.session.query(db.func.avg(Performance.overall_score)).scalar() or 0
    
    # Top performers
    top_performers = Performance.query.filter(Performance.overall_score.isnot(None))\
                                    .order_by(Performance.overall_score.desc())\
                                    .limit(10).all()
    
    # Performance by department
    dept_performance = db.session.query(
        Employee.department,
        db.func.avg(Performance.overall_score).label('avg_score'),
        db.func.count(Performance.id).label('eval_count')
    ).join(Performance).group_by(Employee.department).all()
    
    return render_template('performance/reports.html',
                         total_evaluations=total_evaluations,
                         avg_quality=avg_quality,
                         avg_productivity=avg_productivity,
                         avg_teamwork=avg_teamwork,
                         avg_communication=avg_communication,
                         avg_leadership=avg_leadership,
                         avg_overall=avg_overall,
                         top_performers=top_performers,
                         dept_performance=dept_performance)
