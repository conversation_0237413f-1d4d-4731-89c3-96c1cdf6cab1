import os
from flask import Flask, render_template, render_template_string, request, session, redirect, url_for, flash
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'hr-system-secret-key-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///hr_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
login_manager.login_message_category = 'info'

# User model
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(20), default='user')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def is_authenticated(self):
        return True
    
    def is_anonymous(self):
        return False
    
    def get_id(self):
        return str(self.id)

# Employee model
class Employee(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.String(20), unique=True, nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    first_name_ar = db.Column(db.String(50))
    last_name_ar = db.Column(db.String(50))
    email = db.Column(db.String(120), unique=True)
    phone = db.Column(db.String(20))
    department = db.Column(db.String(100))
    position = db.Column(db.String(100))
    basic_salary = db.Column(db.Float, default=0.0)
    hire_date = db.Column(db.Date, nullable=False, default=date.today)
    status = db.Column(db.String(20), default='active')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"
    
    @property
    def full_name_ar(self):
        if self.first_name_ar and self.last_name_ar:
            return f"{self.first_name_ar} {self.last_name_ar}"
        return self.full_name

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Routes
@app.route('/')
@login_required
def dashboard():
    total_employees = Employee.query.filter_by(status='active').count()
    recent_employees = Employee.query.order_by(Employee.created_at.desc()).limit(5).all()
    
    return render_template_string(DASHBOARD_TEMPLATE, 
                                total_employees=total_employees,
                                recent_employees=recent_employees)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if not username or not password:
            flash('يرجى إدخال اسم المستخدم وكلمة المرور', 'error')
            return render_template_string(LOGIN_TEMPLATE)
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password) and user.is_active:
            login_user(user)
            flash(f'مرحباً {user.username}!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template_string(LOGIN_TEMPLATE)

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

@app.route('/employees')
@login_required
def employees():
    page = request.args.get('page', 1, type=int)
    employees = Employee.query.paginate(page=page, per_page=10, error_out=False)
    return render_template_string(EMPLOYEES_TEMPLATE, employees=employees)

@app.route('/employees/add', methods=['GET', 'POST'])
@login_required
def add_employee():
    if request.method == 'POST':
        employee_id = request.form.get('employee_id')
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        first_name_ar = request.form.get('first_name_ar')
        last_name_ar = request.form.get('last_name_ar')
        email = request.form.get('email')
        phone = request.form.get('phone')
        department = request.form.get('department')
        position = request.form.get('position')
        basic_salary = request.form.get('basic_salary', type=float) or 0.0
        hire_date_str = request.form.get('hire_date')
        
        if not all([employee_id, first_name, last_name, department, position]):
            flash('الحقول المطلوبة: رقم الموظف، الاسم الأول، الاسم الأخير، القسم، المنصب', 'error')
            return render_template_string(ADD_EMPLOYEE_TEMPLATE)
        
        # Check if employee ID already exists
        if Employee.query.filter_by(employee_id=employee_id).first():
            flash('رقم الموظف موجود بالفعل', 'error')
            return render_template_string(ADD_EMPLOYEE_TEMPLATE)
        
        try:
            hire_date = datetime.strptime(hire_date_str, '%Y-%m-%d').date() if hire_date_str else date.today()
        except ValueError:
            flash('تنسيق التاريخ غير صحيح', 'error')
            return render_template_string(ADD_EMPLOYEE_TEMPLATE)
        
        employee = Employee(
            employee_id=employee_id,
            first_name=first_name,
            last_name=last_name,
            first_name_ar=first_name_ar,
            last_name_ar=last_name_ar,
            email=email,
            phone=phone,
            department=department,
            position=position,
            basic_salary=basic_salary,
            hire_date=hire_date
        )
        
        try:
            db.session.add(employee)
            db.session.commit()
            flash(f'تم إضافة الموظف {employee.full_name} بنجاح', 'success')
            return redirect(url_for('employees'))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إضافة الموظف', 'error')
    
    return render_template_string(ADD_EMPLOYEE_TEMPLATE)

# HTML Templates
LOGIN_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة شؤون الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Noto Sans Arabic', sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center; }
        .login-card { background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 1rem; box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1); }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card login-card">
                    <div class="card-header bg-primary text-white text-center py-4">
                        <i class="bi bi-building" style="font-size: 2rem;"></i>
                        <h4 class="mt-2">نظام إدارة شؤون الموظفين</h4>
                    </div>
                    <div class="card-body p-4">
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label class="form-label">اسم المستخدم</label>
                                <input type="text" class="form-control" name="username" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" name="password" required>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">تسجيل الدخول</button>
                            </div>
                        </form>
                        
                        <div class="text-center mt-3">
                            <small class="text-muted">admin / admin123</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
"""

DASHBOARD_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة شؤون الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Noto Sans Arabic', sans-serif; background-color: #f8f9fa; }
        .navbar-brand { font-weight: 600; }
        .stats-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 1rem; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="bi bi-building"></i> نظام إدارة شؤون الموظفين
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/logout">
                    <i class="bi bi-box-arrow-right"></i> تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-3">
                <div class="list-group">
                    <a href="/" class="list-group-item list-group-item-action active">
                        <i class="bi bi-speedometer2"></i> لوحة التحكم
                    </a>
                    <a href="/employees" class="list-group-item list-group-item-action">
                        <i class="bi bi-people"></i> الموظفون
                    </a>
                    <a href="/employees/add" class="list-group-item list-group-item-action">
                        <i class="bi bi-person-plus"></i> إضافة موظف
                    </a>
                </div>
            </div>

            <div class="col-md-9">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <h1 class="h2 mb-4">
                    <i class="bi bi-speedometer2"></i> لوحة التحكم
                </h1>

                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card stats-card text-center">
                            <div class="card-body">
                                <h3>{{ total_employees }}</h3>
                                <p class="mb-0">إجمالي الموظفين</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">الموظفون الجدد</h5>
                    </div>
                    <div class="card-body">
                        {% if recent_employees %}
                            {% for employee in recent_employees %}
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <strong>{{ employee.full_name_ar or employee.full_name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ employee.department }} - {{ employee.position }}</small>
                                </div>
                                <span class="badge bg-primary">{{ employee.employee_id }}</span>
                            </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted">لا توجد موظفون</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
"""

EMPLOYEES_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الموظفون - نظام إدارة شؤون الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Noto Sans Arabic', sans-serif; background-color: #f8f9fa; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="bi bi-building"></i> نظام إدارة شؤون الموظفين
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/logout">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-3">
                <div class="list-group">
                    <a href="/" class="list-group-item list-group-item-action">
                        <i class="bi bi-speedometer2"></i> لوحة التحكم
                    </a>
                    <a href="/employees" class="list-group-item list-group-item-action active">
                        <i class="bi bi-people"></i> الموظفون
                    </a>
                    <a href="/employees/add" class="list-group-item list-group-item-action">
                        <i class="bi bi-person-plus"></i> إضافة موظف
                    </a>
                </div>
            </div>

            <div class="col-md-9">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h2">
                        <i class="bi bi-people"></i> إدارة الموظفين
                    </h1>
                    <a href="/employees/add" class="btn btn-primary">
                        <i class="bi bi-person-plus"></i> إضافة موظف جديد
                    </a>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">قائمة الموظفين ({{ employees.total }})</h5>
                    </div>
                    <div class="card-body p-0">
                        {% if employees.items %}
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم الموظف</th>
                                        <th>الاسم</th>
                                        <th>القسم</th>
                                        <th>المنصب</th>
                                        <th>تاريخ التوظيف</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for employee in employees.items %}
                                    <tr>
                                        <td><strong>{{ employee.employee_id }}</strong></td>
                                        <td>{{ employee.full_name_ar or employee.full_name }}</td>
                                        <td>{{ employee.department or '-' }}</td>
                                        <td>{{ employee.position or '-' }}</td>
                                        <td>{{ employee.hire_date.strftime('%Y-%m-%d') }}</td>
                                        <td>
                                            <span class="badge bg-success">{{ employee.status }}</span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="bi bi-people" style="font-size: 4rem; color: #dee2e6;"></i>
                            <h5 class="mt-3 text-muted">لا توجد موظفون</h5>
                            <a href="/employees/add" class="btn btn-primary">إضافة موظف جديد</a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
"""

ADD_EMPLOYEE_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة موظف - نظام إدارة شؤون الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Noto Sans Arabic', sans-serif; background-color: #f8f9fa; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="bi bi-building"></i> نظام إدارة شؤون الموظفين
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/logout">تسجيل الخروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-3">
                <div class="list-group">
                    <a href="/" class="list-group-item list-group-item-action">
                        <i class="bi bi-speedometer2"></i> لوحة التحكم
                    </a>
                    <a href="/employees" class="list-group-item list-group-item-action">
                        <i class="bi bi-people"></i> الموظفون
                    </a>
                    <a href="/employees/add" class="list-group-item list-group-item-action active">
                        <i class="bi bi-person-plus"></i> إضافة موظف
                    </a>
                </div>
            </div>

            <div class="col-md-9">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <h1 class="h2 mb-4">
                    <i class="bi bi-person-plus"></i> إضافة موظف جديد
                </h1>

                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">بيانات الموظف</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">رقم الموظف *</label>
                                        <input type="text" class="form-control" name="employee_id" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" name="email">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">الاسم الأول (إنجليزي) *</label>
                                        <input type="text" class="form-control" name="first_name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">الاسم الأخير (إنجليزي) *</label>
                                        <input type="text" class="form-control" name="last_name" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">الاسم الأول (عربي)</label>
                                        <input type="text" class="form-control" name="first_name_ar">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">الاسم الأخير (عربي)</label>
                                        <input type="text" class="form-control" name="last_name_ar">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" name="phone">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">تاريخ التوظيف</label>
                                        <input type="date" class="form-control" name="hire_date">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">القسم *</label>
                                        <input type="text" class="form-control" name="department" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">المنصب *</label>
                                        <input type="text" class="form-control" name="position" required>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الراتب الأساسي</label>
                                <input type="number" class="form-control" name="basic_salary" step="0.01" min="0">
                            </div>

                            <div class="d-flex justify-content-between">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-lg"></i> حفظ الموظف
                                </button>
                                <a href="/employees" class="btn btn-secondary">
                                    <i class="bi bi-x-lg"></i> إلغاء
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
"""

# Initialize database and create admin user
if __name__ == '__main__':
    with app.app_context():
        db.create_all()

        # Create default admin user if not exists
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                role='admin'
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
            db.session.commit()
            print("✅ تم إنشاء المستخدم الافتراضي: admin/admin123")

    print("=" * 60)
    print("🎉 نظام إدارة شؤون الموظفين")
    print("=" * 60)
    print("✅ تم تشغيل النظام بنجاح!")
    print("🌐 افتح المتصفح على: http://localhost:5000")
    print("🔑 اسم المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print("=" * 60)
    print("اضغط Ctrl+C لإيقاف النظام")
    print("=" * 60)

    app.run(debug=True, host='0.0.0.0', port=5000)
