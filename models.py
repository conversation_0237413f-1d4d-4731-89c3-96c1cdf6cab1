from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime, date
from werkzeug.security import generate_password_hash, check_password_hash

db = SQLAlchemy()

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(20), default='user')  # admin, hr, user
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.<PERSON>, default=True)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def __repr__(self):
        return f'<User {self.username}>'

class Employee(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.String(20), unique=True, nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    first_name_ar = db.Column(db.String(50))
    last_name_ar = db.Column(db.String(50))
    email = db.Column(db.String(120), unique=True)
    phone = db.Column(db.String(20))
    national_id = db.Column(db.String(20), unique=True)
    birth_date = db.Column(db.Date)
    hire_date = db.Column(db.Date, nullable=False, default=date.today)
    department = db.Column(db.String(100))
    position = db.Column(db.String(100))
    position_ar = db.Column(db.String(100))
    basic_salary = db.Column(db.Float, default=0.0)
    address = db.Column(db.Text)
    emergency_contact = db.Column(db.String(100))
    emergency_phone = db.Column(db.String(20))
    status = db.Column(db.String(20), default='active')  # active, inactive, terminated
    profile_picture = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    payrolls = db.relationship('Payroll', backref='employee', lazy=True, cascade='all, delete-orphan')
    leaves = db.relationship('Leave', backref='employee', lazy=True, cascade='all, delete-orphan')
    attendances = db.relationship('Attendance', backref='employee', lazy=True, cascade='all, delete-orphan')
    performances = db.relationship('Performance', backref='employee', lazy=True, cascade='all, delete-orphan')
    documents = db.relationship('Document', backref='employee', lazy=True, cascade='all, delete-orphan')
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"
    
    @property
    def full_name_ar(self):
        if self.first_name_ar and self.last_name_ar:
            return f"{self.first_name_ar} {self.last_name_ar}"
        return self.full_name
    
    def __repr__(self):
        return f'<Employee {self.employee_id}: {self.full_name}>'

class Payroll(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    month = db.Column(db.Integer, nullable=False)  # 1-12
    year = db.Column(db.Integer, nullable=False)
    basic_salary = db.Column(db.Float, nullable=False)
    allowances = db.Column(db.Float, default=0.0)
    overtime = db.Column(db.Float, default=0.0)
    bonuses = db.Column(db.Float, default=0.0)
    deductions = db.Column(db.Float, default=0.0)
    taxes = db.Column(db.Float, default=0.0)
    insurance = db.Column(db.Float, default=0.0)
    net_salary = db.Column(db.Float, nullable=False)
    payment_date = db.Column(db.Date)
    status = db.Column(db.String(20), default='pending')  # pending, paid, cancelled
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def calculate_net_salary(self):
        gross = self.basic_salary + self.allowances + self.overtime + self.bonuses
        total_deductions = self.deductions + self.taxes + self.insurance
        self.net_salary = gross - total_deductions
        return self.net_salary
    
    def __repr__(self):
        return f'<Payroll {self.employee.full_name} - {self.month}/{self.year}>'

class Leave(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    leave_type = db.Column(db.String(50), nullable=False)  # annual, sick, emergency, maternity
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    days_count = db.Column(db.Integer, nullable=False)
    reason = db.Column(db.Text)
    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected
    approved_by = db.Column(db.String(100))
    approved_date = db.Column(db.Date)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<Leave {self.employee.full_name} - {self.leave_type}>'

class Attendance(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    check_in = db.Column(db.Time)
    check_out = db.Column(db.Time)
    break_duration = db.Column(db.Integer, default=0)  # minutes
    overtime_hours = db.Column(db.Float, default=0.0)
    status = db.Column(db.String(20), default='present')  # present, absent, late, half_day
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    @property
    def working_hours(self):
        if self.check_in and self.check_out:
            from datetime import datetime, timedelta
            check_in_dt = datetime.combine(date.today(), self.check_in)
            check_out_dt = datetime.combine(date.today(), self.check_out)
            if check_out_dt < check_in_dt:
                check_out_dt += timedelta(days=1)
            total_minutes = (check_out_dt - check_in_dt).total_seconds() / 60
            working_minutes = total_minutes - self.break_duration
            return working_minutes / 60  # return hours
        return 0

    def __repr__(self):
        return f'<Attendance {self.employee.full_name} - {self.date}>'

class Performance(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    evaluation_period = db.Column(db.String(50), nullable=False)  # Q1 2024, Annual 2024, etc.
    evaluator = db.Column(db.String(100), nullable=False)
    quality_score = db.Column(db.Integer)  # 1-10
    productivity_score = db.Column(db.Integer)  # 1-10
    teamwork_score = db.Column(db.Integer)  # 1-10
    communication_score = db.Column(db.Integer)  # 1-10
    leadership_score = db.Column(db.Integer)  # 1-10
    overall_score = db.Column(db.Float)
    strengths = db.Column(db.Text)
    areas_for_improvement = db.Column(db.Text)
    goals = db.Column(db.Text)
    comments = db.Column(db.Text)
    evaluation_date = db.Column(db.Date, nullable=False, default=date.today)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def calculate_overall_score(self):
        scores = [
            self.quality_score or 0,
            self.productivity_score or 0,
            self.teamwork_score or 0,
            self.communication_score or 0,
            self.leadership_score or 0
        ]
        valid_scores = [s for s in scores if s > 0]
        if valid_scores:
            self.overall_score = sum(valid_scores) / len(valid_scores)
        return self.overall_score

    def __repr__(self):
        return f'<Performance {self.employee.full_name} - {self.evaluation_period}>'

class Document(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    document_type = db.Column(db.String(50), nullable=False)  # contract, certificate, id_copy, etc.
    file_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.Integer)
    mime_type = db.Column(db.String(100))
    uploaded_by = db.Column(db.String(100))
    upload_date = db.Column(db.DateTime, default=datetime.utcnow)
    notes = db.Column(db.Text)

    def __repr__(self):
        return f'<Document {self.title} - {self.employee.full_name}>'
