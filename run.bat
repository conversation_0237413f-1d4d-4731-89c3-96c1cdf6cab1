@echo off
echo ========================================
echo    نظام إدارة شؤون الموظفين
echo ========================================
echo.

echo تحقق من وجود Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.8 أو أحدث من https://python.org
    pause
    exit /b 1
)

echo تثبيت المتطلبات...
python -m pip install -r requirements.txt

if errorlevel 1 (
    echo خطأ في تثبيت المتطلبات
    pause
    exit /b 1
)

echo.
echo تشغيل التطبيق...
echo يمكنك الوصول للتطبيق على: http://localhost:5000
echo بيانات تسجيل الدخول الافتراضية:
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo.
echo اضغط Ctrl+C لإيقاف التطبيق
echo.

python app.py

pause
