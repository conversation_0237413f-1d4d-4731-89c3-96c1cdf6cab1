# دليل البدء السريع - نظام إدارة شؤون الموظفين

## 🚀 التشغيل السريع

### الطريقة الأولى: اختبار سريع
```bash
python test_app.py
```
ثم افتح المتصفح على: http://localhost:5000

### الطريقة الثانية: تشغيل النظام الكامل

#### على Windows:
1. انقر نقراً مزدوجاً على `run.bat`
2. انتظر تثبيت المتطلبات
3. افتح المتصفح على: http://localhost:5000

#### على Linux/Mac:
```bash
chmod +x run.sh
./run.sh
```

## 🔑 بيانات تسجيل الدخول

- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## 📋 الميزات الرئيسية

### ✅ إدارة الموظفين
- إضافة وتعديل بيانات الموظفين
- رفع صور الموظفين
- دعم الأسماء بالعربية والإنجليزية
- تتبع حالة الموظف

### 💰 إدارة الرواتب
- إنشاء كشوف الرواتب
- حساب الاستحقاقات والخصومات
- طباعة PDF
- إنشاء رواتب شهرية جماعية

### 🏖️ إدارة الإجازات
- تقديم طلبات الإجازة
- اعتماد/رفض الطلبات
- تتبع أيام الإجازة

### ⏰ الحضور والانصراف
- تسجيل أوقات الحضور
- حساب ساعات العمل
- تقارير الحضور

### 📊 تقييم الأداء
- تقييمات دورية
- معايير متعددة
- تاريخ التقييمات

### 📁 إدارة المستندات
- رفع الملفات
- تصنيف المستندات
- تحميل وعرض

## 🛠️ المتطلبات

- Python 3.8+
- متصفح ويب حديث

## 📁 هيكل المشروع

```
anwar/
├── app.py              # التطبيق الرئيسي
├── test_app.py         # اختبار سريع
├── run.bat            # تشغيل Windows
├── run.sh             # تشغيل Linux/Mac
├── requirements.txt   # المتطلبات
├── config.py          # الإعدادات
├── models.py          # قاعدة البيانات
├── blueprints/        # وحدات النظام
├── templates/         # قوالب HTML
└── static/           # الملفات الثابتة
```

## 🔧 إعدادات متقدمة

### تغيير قاعدة البيانات إلى PostgreSQL:
1. أنشئ ملف `.env`:
```
DATABASE_URL=postgresql://username:password@localhost/hr_system
```

### تخصيص الإعدادات:
عدّل ملف `config.py` حسب احتياجاتك.

## 🆘 حل المشاكل الشائعة

### Python غير موجود:
- ثبت Python من https://python.org
- تأكد من إضافته إلى PATH

### خطأ في المتطلبات:
```bash
python -m pip install --upgrade pip
python -m pip install -r requirements.txt
```

### مشكلة في قاعدة البيانات:
احذف ملف `hr_system.db` وأعد تشغيل التطبيق.

## 📞 الدعم

للمساعدة أو الإبلاغ عن مشاكل، أنشئ issue في المستودع.

---
© 2024 نظام إدارة شؤون الموظفين
